using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System.Collections.Generic;
#nullable disable

namespace PlatformContext.Modules.Brand
{
    public class Brand
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string BrandType { get; set; } = string.Empty;
        public Theme Theme { get; set; } = new Theme();
        public Logo Logo { get; set; } = new Logo();
        public string[] Hosts { get; set; } = Array.Empty<string>();
        public Footer Footer { get; set; } = new Footer();
        public GtwData GtwData { get; set; } = new GtwData();
        public string GTM { get; set; } = string.Empty;
        public string OmnilogicScript { get; set; } = string.Empty;
        public string ImageProcessorPath { get; set; } = string.Empty;
        public HeaderImageBg HeaderImageBg { get; set; } = new HeaderImageBg();
        public CustomImageBgProduct CustomImageBgProduct { get; set; } = new CustomImageBgProduct();
        public string HomeMotorImageBg { get; set; } = string.Empty;
        public FavIcon Favicon { get; set; } = new FavIcon();
        public Dictionary<string, string> HomeMessages { get; set; } = new Dictionary<string, string>();
        public HotjarIds HotjarIds { get; set; } = new HotjarIds();
        public string TermConditionLink { get; set; } = string.Empty;
        public string PrivacyLink { get; set; } = string.Empty;
        public string[] FeatureToggles { get; set; } = Array.Empty<string>();
        public string PendingPaymentContact { get; set; } = string.Empty;
        public List<ProductRedirectInfo> ProductsRedirect { get; set; } = new List<ProductRedirectInfo>();
        public string[] RedirectsToLogin { get; set; } = Array.Empty<string>();

    }

    public class Theme
    {
        public string ThemeName { get; set; } = string.Empty;
        public string Primary { get; set; } = string.Empty;
        public string Secondary { get; set; } = string.Empty;
        // Outras propriedades relacionadas ao tema podem ser adicionadas aqui
    }

    public class Logo
    {
        public string Dark { get; set; } = string.Empty;
        public string Light { get; set; } = string.Empty;
        public string PreferredWidth { get; set; } = string.Empty;
        public string PreferredWidthMobile { get; set; } = string.Empty;
    }

    public class Footer
    {
        public List<FooterSection> Sections { get; set; } = new List<FooterSection>();
        public string AppTitle { get; set; } = string.Empty;
        public List<App> Apps { get; set; } = new List<App>();
        public string SocialNetworksTitle { get; set; } = string.Empty;
        public List<SocialNetwork> SocialNetworks { get; set; } = new List<SocialNetwork>();
        public List<Payment> Payments { get; set; } = new List<Payment>();
        public List<ImportantReminder> ImportantReminders { get; set; } = new List<ImportantReminder>();
    }

    public class FooterSection
    {
        public string Title { get; set; } = string.Empty;
        public List<FooterItem> Items { get; set; } = new List<FooterItem>();
    }

    public class FooterItem
    {
        public string Type { get; set; } = string.Empty;
        public string Text { get; set; } = string.Empty;
        public Dictionary<string, string> Attributes { get; set; } = new Dictionary<string, string>();
    }

    public class App
    {
        public string Name { get; set; } = string.Empty;
        public string Image { get; set; } = string.Empty;
        public string Url { get; set; } = string.Empty;
    }

    public class SocialNetwork
    {
        public string Name { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public Dictionary<string, string> Attributes { get; set; } = new Dictionary<string, string>();
    }

    public class Payment
    {
        public string Title { get; set; } = string.Empty;
        
        public string Description { get; set; } = string.Empty;

        public List<PaymentFlag> Flags { get; set; } = new List<PaymentFlag>();
    }

    public class PaymentFlag
    {
        public string Name { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
    }

    public class ImportantReminder
    {
        public string Label { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
    }

    public class GtwData
    {
        public string BranchId { get; set; } = string.Empty;
        public string AgentSign { get; set; } = string.Empty;
    }

    public class HeaderImageBg
    {
        public string Desktop { get; set; } = string.Empty;
        public string Mobile { get; set; } = string.Empty;
    }

    public class CustomImageBgProduct
    {
        public Dictionary<string, ProductName> ProductNames { get; set; } = new Dictionary<string, ProductName>();
    }

    public class ProductName
    {
        public string Desktop { get; set; } = string.Empty;
        public string Mobile { get; set; } = string.Empty;
    }

    public class FavIcon
    {
        public string Small { get; set; } = string.Empty;
        public string Big { get; set; } = string.Empty;
    }

    public class HotjarIds
    {
        public string MyTravelsFront { get; set; } = string.Empty;
        public string CustomerLoyalty { get; set; } = string.Empty;
    }

    public class ProductRedirectInfo
    {
        public string Name { get; set; } = string.Empty;
        public string Url { get; set; } = string.Empty;
    }
}
