

namespace PlatformContext.Modules.Headers
{
    public class GetHeaderByIdUseCase
{
    private readonly IHeadersRepository _headersRepository;

    public GetHeaderByIdUseCase(IHeadersRepository headersRepository)
    {
        _headersRepository = headersRepository;
    }

    public async Task<Header> ExecuteAsync(string id)
    {
        if (string.IsNullOrWhiteSpace(id))
            throw new ArgumentException("Id cannot be null or empty.", nameof(id));

        return await _headersRepository.GetByIdAsync(id);
    }
}

}