using Microsoft.AspNetCore.Mvc;
using PlatformContext.Modules.Navigation.Dtos;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PlatformContext.Modules.Navigation
{
    [ApiController]
    [Route("api/navigation")]
    public class NavigationController : ControllerBase
    {
        private readonly CreateNavigationUseCase _createNavigationUseCase;
        private readonly ListAllNavigationsUseCase _listAllNavigationsUseCase;
        private readonly GetNavigationByIdUseCase _getNavigationByIdUseCase;
        private readonly UpdateNavigationUseCase _updateNavigationUseCase;
        private readonly DeleteNavigationUseCase _deleteNavigationUseCase;

        public NavigationController(
            CreateNavigationUseCase createNavigationUseCase,
            ListAllNavigationsUseCase listAllNavigationsUseCase,
            GetNavigationByIdUseCase getNavigationByIdUseCase,
            UpdateNavigationUseCase updateNavigationUseCase,
            DeleteNavigationUseCase deleteNavigationUseCase)
        {
            _createNavigationUseCase = createNavigationUseCase;
            _listAllNavigationsUseCase = listAllNavigationsUseCase;
            _getNavigationByIdUseCase = getNavigationByIdUseCase;
            _updateNavigationUseCase = updateNavigationUseCase;
            _deleteNavigationUseCase = deleteNavigationUseCase;
        }

        [HttpPost]
        public async Task<IActionResult> Create([FromBody] NavigationDto navigationDto)
        {
            var navigation = await _createNavigationUseCase.ExecuteAsync(navigationDto);
            return CreatedAtAction(nameof(GetById), new { id = navigation.Id }, new
            {
                message = "Navegação criada com sucesso.",
                data = navigation
            });
        }

        [HttpGet]
        public async Task<IActionResult> GetAll([FromQuery] int page = 1, [FromQuery] int pageSize = 10)
        {
            var (navigations, total) = await _listAllNavigationsUseCase.ExecuteAsync(page, pageSize);
            return Ok(new
            {
                total,
                data = navigations
            });
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(string id)
        {
            var navigation = await _getNavigationByIdUseCase.ExecuteAsync(id);
            return Ok(navigation);
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Update(string id, [FromBody] NavigationDto navigationDto)
        {
            var navigation = await _updateNavigationUseCase.ExecuteAsync(id, navigationDto);
            return Ok(new
            {
                message = "Navegação atualizada com sucesso.",
                data = navigation
            });
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(string id)
        {
            var result = await _deleteNavigationUseCase.ExecuteAsync(id);
            if (!result)
            {
                return NotFound(new { message = "Navegação não encontrada." });
            }
            return Ok(new { message = "Navegação deletada com sucesso." });
        }
    }
}
