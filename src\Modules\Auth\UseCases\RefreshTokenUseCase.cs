using Auth.DTOs;
using Auth.Repositories;

namespace Auth.UseCases
{
    public class RefreshTokenUseCase
    {
        private readonly IAuthRepository _authRepository;

        public RefreshTokenUseCase(IAuthRepository authRepository)
        {
            _authRepository = authRepository;
        }

        public async Task<TokenResponseDto> ExecuteAsync(string refreshToken)
        {
            if (string.IsNullOrWhiteSpace(refreshToken))
            {
                throw new ArgumentException("Refresh token não pode ser vazio.");
            }

            try
            {
                return await _authRepository.RefreshTokenAsync(refreshToken);
            }
            catch (HttpRequestException ex)
            {
                Console.WriteLine($"Erro ao atualizar o token: {ex.Message}");
                throw new UnauthorizedAccessException("Erro ao realizar o refresh do token. Verifique as credenciais ou permissões.", ex);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erro inesperado ao atualizar o token: {ex.Message}");
                throw;
            }
        }
    }
}
