using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Auth.Repositories;

namespace Auth.Middlewares
{
    public class AuthenticationMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly IAuthRepository _authRepository;

        public AuthenticationMiddleware(RequestDelegate next, IAuthRepository authRepository)
        {
            _next = next;
            _authRepository = authRepository;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            if (context.Request.Path.StartsWithSegments("/api/auth/login") || 
                context.Request.Path.StartsWithSegments("/api/auth/refresh-token") ||
                context.Request.Path.StartsWithSegments("/health") || 
                context.Request.Path.StartsWithSegments("/api/Engine") || 
                context.Request.Path.StartsWithSegments("/api/Headers") || 
                context.Request.Path.StartsWithSegments("/api/Context") ||
                context.Request.Path.StartsWithSegments("/api/site/public")
                )
            {
                await _next(context);
                return;
            }

            var token = context.Request.Headers["Authorization"].FirstOrDefault()?.Split(" ").Last();

            if (string.IsNullOrEmpty(token))
            {
                context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                await context.Response.WriteAsync("Missing Authorization Token");
                return;
            }

            try
            {
                var isValid = await _authRepository.ValidateTokenAsync(token);
                if (!isValid)
                {
                    context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                    await context.Response.WriteAsync("Invalid or Expired Token");
                    return;
                }
            }
            catch (Exception ex)
            {
                context.Response.StatusCode = StatusCodes.Status500InternalServerError;
                await context.Response.WriteAsync($"Token validation error: {ex.Message}");
                return;
            }

            await _next(context);
        }
    }
}
