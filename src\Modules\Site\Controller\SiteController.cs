using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using Swashbuckle.AspNetCore.Annotations;
using PlatformContext.Modules.Site.Dtos;
using Microsoft.AspNetCore.Authorization;

namespace PlatformContext.Modules.Site
{
    [ApiController]
    [Route("api/site")]    public class SiteController : ControllerBase
    {
        private readonly CreateSiteUseCase _createSiteUseCase;
        private readonly GetSiteByIdUseCase _getSiteByIdUseCase;
        private readonly UpdateSiteUseCase _updateSiteUseCase;
        private readonly DeleteSiteUseCase _deleteSiteUseCase;
        private readonly ListAllSitesUseCase _listAllSitesUseCase;

        public SiteController(
            CreateSiteUseCase createSiteUseCase,
            GetSiteByIdUseCase getSiteByIdUseCase,
            UpdateSiteUseCase updateSiteUseCase,
            DeleteSiteUseCase deleteSiteUseCase,
            ListAllSitesUseCase listAllSitesUseCase)
        {
            _createSiteUseCase = createSiteUseCase;
            _getSiteByIdUseCase = getSiteByIdUseCase;
            _updateSiteUseCase = updateSiteUseCase;
            _deleteSiteUseCase = deleteSiteUseCase;
            _listAllSitesUseCase = listAllSitesUseCase;
        }

        [HttpPost]
        public async Task<IActionResult> Create([FromBody] SiteDto siteDto)
        {
            var createdSite = await _createSiteUseCase.ExecuteAsync(siteDto);
            if (createdSite == null)
                return BadRequest("Falha ao criar o site.");

            return CreatedAtAction(nameof(GetById), new { id = createdSite.Id }, new
            {
                message = "Site criado com sucesso",
                data = createdSite
            });
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(string id)
        {
            try
            {
                var site = await _getSiteByIdUseCase.ExecuteAsync(id);
                return Ok(site);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
        }

        [HttpPatch("{id}")]
        public async Task<IActionResult> Update(string id, [FromBody] SiteDto siteDto)
        {
            try
            {
                var updatedSite = await _updateSiteUseCase.ExecuteAsync(id, siteDto);
                return Ok(new
                {
                    message = "Site atualizado com sucesso",
                    data = updatedSite
                });
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(string id)
        {
            var result = await _deleteSiteUseCase.ExecuteAsync(id);
            if (!result)
                return NotFound("Site não encontrado para exclusão.");

            return Ok(new { message = "Site deletado com sucesso" });
        }

        [HttpGet]
        public async Task<IActionResult> GetAll([FromQuery] string? siteName, [FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10)
        {
            var (sites, totalRecords) = await _listAllSitesUseCase.ExecuteAsync(siteName, pageNumber, pageSize);
            var response = new
            {
                data = sites,
                totalRecords,
                pageNumber,
                pageSize
            };

            return Ok(response);
        }

        [HttpGet("public")]
        public async Task<IActionResult> GetAllPublic([FromQuery] string? siteName, [FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10)
        {
            // Verifique o token ou qualquer lógica necessária no middleware que será executado antes desta ação
            var (sites, totalRecords) = await _listAllSitesUseCase.ExecuteAsync(siteName, pageNumber, pageSize);
            var response = new
            {
                data = sites,
                totalRecords,
                pageNumber,
                pageSize
            };

            return Ok(response);
        }


    }
}
