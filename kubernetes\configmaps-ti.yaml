apiVersion: v1
kind: ConfigMap
metadata:
  name: corp-platform-context
  namespace: corp-platform
data:
  ADDITIONAL_OPTS: " "
  PREFIX: corp-platform-context
  VAULT_HOST: vault-dev.services.cvc.com.br
  VAULT_SCHEME: http
  CONSUL_HOST: consul-dev.services.cvc.com.br
  CONSUL_PORT: "8500"
  ASPNETCORE_ENVIRONMENT: ti
  PROJECT_NAME: sub-backend-flights
  MongoDbSettings__ConnectionString: "vault:secret/corp-platform-context#MONGO_HOST"
  MongoDbSettings__DatabaseName: "vault:secret/corp-platform-context#MONGO_NAME"