using System.Threading.Tasks;

namespace PlatformContext.Modules.Navigation
{
    public class DeleteNavigationUseCase
    {
        private readonly INavigationRepository _navigationRepository;

        public DeleteNavigationUseCase(INavigationRepository navigationRepository)
        {
            _navigationRepository = navigationRepository;
        }

        public async Task<bool> ExecuteAsync(string id)
        {
            var navigation = await _navigationRepository.GetByIdAsync(id);
            if (navigation == null)
            {
                return false;
            }

            await _navigationRepository.DeleteAsync(id);
            return true;
        }
    }
}