@Library('cvc-jen<PERSON>-lib')

final def projectConfig = this.readJSON(text: """
{
    "name": "corp-platform-context",
    "git": {
        "repositoryUrl": "******************:corp-platform/corp-platform-context.git"
    },
    "technology": {
        "name": "nodejs",
        "version": "14.4.0",
        "buildCommands": {
            "buildApp": "" 
        }
    },
    "docker": {
        "dockerfilePath": "Dockerfile"
    },
    "kubernetes": {
        "namespace": "corp-platform"
    }
}
""")
  
final commons = cvcCorpPipeline.getCommons(this, projectConfig)

pipeline {
    agent any
    stages {
        stage('Deploy TI') {
            steps {
                script {
                    deploy(this, 'TI', projectConfig, commons) {
                        
                    }
                }
            }
        }
        stage('Deploy QA') {
            steps {
                script {
                    deploy(this, 'QA', projectConfig, commons) {

                    }
                }
            }
        }
        stage('Deploy PROD') {
            steps {
                script {
                    deploy(this, 'PROD', projectConfig, commons) {

                    }
                }
            }
        }
    }
}