using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using PlatformContext.Modules.Brand.Dtos;

namespace PlatformContext.Modules.Brand
{
    [ApiController]
    [Route("api/brand")]
    public class BrandController : ControllerBase
    {
        private readonly CreateBrandUseCase _createBrandUseCase;
        private readonly ListAllBrandsUseCase _listAllBrandsUseCase;
        private readonly GetBrandByIdUseCase _getBrandByIdUseCase;
        private readonly UpdateBrandUseCase _updateBrandUseCase;
        private readonly DeleteBrandUseCase _deleteBrandUseCase;
        private readonly IBrandRepository _brandRepository;

        public BrandController(
            ListAllBrandsUseCase listAllBrandsUseCase,
            CreateBrandUseCase createBrandUseCase,
            GetBrandByIdUseCase getBrandByIdUseCase,
            UpdateBrandUseCase updateBrandUseCase,
            DeleteBrandUseCase deleteBrandUseCase,
            IBrandRepository brandRepository)
        {
            _createBrandUseCase = createBrandUseCase;
            _listAllBrandsUseCase = listAllBrandsUseCase;
            _getBrandByIdUseCase = getBrandByIdUseCase;
            _updateBrandUseCase = updateBrandUseCase;
            _deleteBrandUseCase = deleteBrandUseCase;
            _brandRepository = brandRepository;
        }

        [HttpPost]
        public async Task<IActionResult> Create([FromBody] BrandDto brandDto)
        {
            var createdBrand = await _createBrandUseCase.ExecuteAsync(brandDto);

            if (createdBrand == null)
                return BadRequest("Falha ao criar a Brand.");

            return CreatedAtAction(nameof(GetById), new { id = createdBrand.Id }, new
            {
                message = "Brand criada com sucesso",
                data = createdBrand
            });
        }
        
        [HttpGet]
        public async Task<IActionResult> GetAll([FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10)
        {
            var (brands, totalCount) = await _listAllBrandsUseCase.ExecuteAsync(pageNumber, pageSize);
            return Ok(new
            {
                data = brands,
                totalCount,
                pageNumber,
                pageSize
            });
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(string id)
        {
            var brand = await _getBrandByIdUseCase.ExecuteAsync(id);
            if (brand == null)
                return NotFound(new { message = $"Brand with ID {id} not found." });

            return Ok(brand);
        }

       [HttpPatch("{id}")]
        public async Task<IActionResult> Update(string id, [FromBody] BrandDto brandDto)
        {
            try
            {
                await _updateBrandUseCase.ExecuteAsync(id, brandDto);

                return Ok(new
                {
                    message = "Brand atualizada com sucesso"
                });
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new
                {
                    message = ex.Message
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    message = "Erro ao atualizar Brand",
                    details = ex.Message
                });
            }
        }



        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(string id)
        {
            var result = await _deleteBrandUseCase.ExecuteAsync(id);

            if (!result)
                return NotFound("Brand não encontrada para exclusão.");

            return Ok(new { message = "Brand deletada com sucesso" });
        }
    }
}
