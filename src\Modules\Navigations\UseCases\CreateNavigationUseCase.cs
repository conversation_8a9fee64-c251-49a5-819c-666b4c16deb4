using PlatformContext.Modules.Navigation.Dtos;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PlatformContext.Modules.Navigation
{
    public class CreateNavigationUseCase
    {
        private readonly INavigationRepository _navigationRepository;

        public CreateNavigationUseCase(INavigationRepository navigationRepository)
        {
            _navigationRepository = navigationRepository;
        }

        public async Task<Navigation> ExecuteAsync(NavigationDto navigationDto)
        {
            var navigation = new Navigation
            {
                Name = navigationDto.Name,
                Items = navigationDto.Items.Select(item => new NavigationItem
                {
                    Label = item.Label,
                    Url = item.Url,
                    Icon = item.Icon,
                    IsActive = item.IsActive
                }).ToList()
            };

            await _navigationRepository.CreateAsync(navigation);
            return navigation;
        }
    }
}