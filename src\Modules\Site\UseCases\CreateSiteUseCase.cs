using System.Collections.Generic;
using System.Threading.Tasks;
using PlatformContext.Modules.Site.Dtos;
using PlatformContext.Modules.Brand;
using System;

namespace PlatformContext.Modules.Site
{
    public class CreateSiteUseCase
    {
        private readonly ISiteRepository _siteRepository;
        private readonly IBrandRepository _brandRepository;

        public CreateSiteUseCase(ISiteRepository siteRepository, IBrandRepository brandRepository)
        {
            _siteRepository = siteRepository;
            _brandRepository = brandRepository;
        }

        public async Task<Site> ExecuteAsync(SiteDto siteDto)
        {
            var brand = await _brandRepository.GetByIdAsync(siteDto.BrandId);
            if (brand == null)
            {
                throw new KeyNotFoundException($"Brand com ID {siteDto.BrandId} não encontrada.");
            }

            var site = new Site
            {
                Name = siteDto.Name,
                Description = siteDto.Description,
                BrandId = siteDto.BrandId,
                NavigationId = siteDto.NavigationId,
                HeaderId = siteDto.HeaderId,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            await _siteRepository.CreateAsync(site);
            return site;
        }
    }
}
