using System.Collections.Generic;

namespace PlatformContext.Services.Model
{
    public class EngineResponse
    {
        public Navigation Navigation { get; set; } = new Navigation();
    }

    public class Navigation
    {
        public List<NavigationItem> Items { get; set; } = new List<NavigationItem>();
    }

    public class NavigationItem
    {
        public string Id { get; set; }
        public string Label { get; set; }
        public string Href { get; set; }
        public string Icon { get; set; }
    }
}
