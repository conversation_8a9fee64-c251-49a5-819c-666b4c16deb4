using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using PlatformContext.Modules.Headers;
using System;
using System.Collections.Generic;

namespace PlatformContext.Modules.Site
{
    public class Site
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string? Id { get; set; } = string.Empty;

        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;

        [BsonRepresentation(BsonType.ObjectId)]
        public string BrandId { get; set; } = string.Empty;

        [BsonIgnore]
        public Brand.Brand? Brand { get; set; } // Objeto completo de Brand

        [BsonRepresentation(BsonType.ObjectId)]
        public string NavigationId { get; set; } = string.Empty;

        [BsonIgnore]
        public Navigation.Navigation? Navigation { get; set; } // Objeto completo de Navigation

        [BsonRepresentation(BsonType.ObjectId)]
        public string HeaderId { get; set; } = string.Empty;

        [BsonIgnore]
        public Header? Header { get; set;}

        public bool IsActive { get; set; } = true;
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }
}
