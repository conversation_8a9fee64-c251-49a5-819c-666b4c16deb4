using Auth.DTOs;
using Auth.UseCases;
using Microsoft.AspNetCore.Mvc;

namespace Auth.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly LoginUseCase _loginUseCase;
        private readonly RefreshTokenUseCase _refreshTokenUseCase;

        public AuthController(LoginUseCase loginUseCase, RefreshTokenUseCase refreshTokenUseCase)
        {
            _loginUseCase = loginUseCase;
            _refreshTokenUseCase = refreshTokenUseCase;
        }

        [HttpPost("login")]
        public async Task<IActionResult> Login([FromBody] LoginRequestDto loginRequest)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            try
            {
                var tokenResponse = await _loginUseCase.ExecuteAsync(loginRequest);
                return Ok(tokenResponse);
            }
            catch (HttpRequestException ex)
            {
                Console.WriteLine($"Erro ao executar o login: {ex.Message}");
                return StatusCode(StatusCodes.Status401Unauthorized, new
                {
                    timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                    errorCode = "-1011",
                    message = "Usuário ou senha inválida"
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erro inesperado: {ex.Message}");
                return StatusCode(StatusCodes.Status500InternalServerError, new
                {
                    timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                    message = "Erro interno do servidor"
                });
            }
        }

        [HttpPost("refresh-token")]
        public async Task<IActionResult> RefreshToken([FromBody] RefreshTokenRequestDto refreshTokenRequest)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            try
            {
                var tokenResponse = await _refreshTokenUseCase.ExecuteAsync(refreshTokenRequest.RefreshToken);
                return Ok(tokenResponse);
            }
            catch (UnauthorizedAccessException ex)
            {
                Console.WriteLine($"Erro ao realizar refresh token: {ex.Message}");
                return StatusCode(StatusCodes.Status401Unauthorized, new
                {
                    timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                    errorCode = "invalid_request",
                    message = "Unauthorized"
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erro inesperado ao realizar refresh token: {ex.Message}");
                return StatusCode(StatusCodes.Status500InternalServerError, new
                {
                    timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                    message = "Erro interno do servidor"
                });
            }
        }
    }
}
