environment:
  - name: TI
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 512Mi
                cpu: 250m
              limits:
                memory: 1024Mi
                cpu: 500m
            readinessProbe:
              health-check:
                path: /health
                port: 80
              initialDelaySeconds: 5
            livenessProbe:
              health-check:
                path: /health
                port: 80
              initialDelaySeconds: 5
            ports:
              - containerPort: 80
          hpa:
            minReplicas: 4
            maxReplicas: 10
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 160
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 160
          service:
            ports:
              - name: mainport
                port: 80
              - name: secport
                port: 5005
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: corp-platform-context
                service:
                  port:
                    number: 80
          configmap:
            data:
              - name: CONSUL_HOST
                value: consul-dev.services.cvc.com.br
              - name: CONSUL_PORT
                value: "8500"
              - name: ASPNETCORE_ENVIRONMENT
                value: ti
              - name: PROJECT_NAME
                value: sub-backend-flights
              - name: ADDITIONAL_OPTS
                value: ' '
              - name: PREFIX
                value: corp-platform-context
              - name: VAULT_HOST
                value: vault-dev.services.cvc.com.br
              - name: VAULT_SCHEME
                value: http
              - name: MongoDbSettings__ConnectionString
                value: "vault:secret/corp-platform-context#MONGO_HOST"
              - name: MongoDbSettings__DatabaseName
                value: "vault:secret/corp-platform-context#MONGO_NAME"
  - name: QA-TMP
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 512Mi
                cpu: 250m
              limits:
                memory: 1024Mi
                cpu: 500m
            readinessProbe:
              health-check:
                path: /health
                port: 80
              initialDelaySeconds: 5
            livenessProbe:
              health-check:
                path: /health
                port: 80
              initialDelaySeconds: 5
            ports:
              - containerPort: 80
          hpa:
            minReplicas: 4
            maxReplicas: 10
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 160
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 160
          service:
            ports:
              - name: mainport
                port: 80
              - name: secport
                port: 5005
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: corp-platform-context
                service:
                  port:
                    number: 80
          configmap:
            data:
              - name: CONSUL_PORT
                value: "8500"
              - name: ASPNETCORE_ENVIRONMENT
                value: qa
              - name: PROJECT_NAME
                value: sub-backend-flights
              - name: ADDITIONAL_OPTS
                value: ' '
              - name: PREFIX
                value: corp-platform-context
              - name: VAULT_HOST
                value: vault.qa.cvc.intra
              - name: VAULT_SCHEME
                value: http
              - name: CONSUL_HOST
                value: consul.qa.cvc.intra
              - name: MongoDbSettings__ConnectionString
                value: "vault:secret/corp-platform-context#MONGO_HOST"
              - name: MongoDbSettings__DatabaseName
                value: "vault:secret/corp-platform-context#MONGO_NAME"

  - name: QA
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 512Mi
                cpu: 250m
              limits:
                memory: 1024Mi
                cpu: 500m
            readinessProbe:
              health-check:
                path: /health
                port: 80
              initialDelaySeconds: 5
            livenessProbe:
              health-check:
                path: /health
                port: 80
              initialDelaySeconds: 5
            ports:
              - containerPort: 80
          hpa:
            minReplicas: 4
            maxReplicas: 10
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 160
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 160
          service:
            ports:
              - name: mainport
                port: 80
              - name: secport
                port: 5005
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: corp-platform-context
                service:
                  port:
                    number: 80
          configmap:
            data:
              - name: CONSUL_PORT
                value: "8500"
              - name: ASPNETCORE_ENVIRONMENT
                value: qa
              - name: PROJECT_NAME
                value: sub-backend-flights
              - name: ADDITIONAL_OPTS
                value: ' '
              - name: PREFIX
                value: corp-platform-context
              - name: VAULT_HOST
                value: vault.qa.cvc.intra
              - name: VAULT_SCHEME
                value: http
              - name: CONSUL_HOST
                value: consul.qa.cvc.intra
              - name: MongoDbSettings__ConnectionString
                value: "vault:secret/corp-platform-context#MONGO_HOST"
              - name: MongoDbSettings__DatabaseName
                value: "vault:secret/corp-platform-context#MONGO_NAME"
  - name: PILOT
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 512Mi
                cpu: 250m
              limits:
                memory: 1024Mi
                cpu: 500m
            readinessProbe:
              health-check:
                path: /health
                port: 80
              initialDelaySeconds: 5
            livenessProbe:
              health-check:
                path: /health
                port: 80
              initialDelaySeconds: 5
            ports:
              - containerPort: 80
          hpa:
            minReplicas: 4
            maxReplicas: 10
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 160
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 160
          service:
            ports:
              - name: mainport
                port: 80
              - name: secport
                port: 5005
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: corp-platform-context
                service:
                  port:
                    number: 80
          configmap:
            data:
              - name: ADDITIONAL_OPTS
                value: ' '
              - name: PREFIX
                value: corp-platform-context
              - name: VAULT_HOST
                value: vault.prod.cvc.intra
              - name: VAULT_SCHEME
                value: http
              - name: CONSUL_HOST
                value: consul.prod.cvc.intra
              - name: CONSUL_PORT
                value: "8500"
              - name: ASPNETCORE_ENVIRONMENT
                value: prod
              - name: PROJECT_NAME
                value: sub-backend-flights
  - name: PROD
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 512Mi
                cpu: 250m
              limits:
                memory: 1024Mi
                cpu: 500m
            readinessProbe:
              health-check:
                path: /health
                port: 80
              initialDelaySeconds: 5
            livenessProbe:
              health-check:
                path: /health
                port: 80
              initialDelaySeconds: 5
            ports:
              - containerPort: 80
          hpa:
            minReplicas: 4
            maxReplicas: 10
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 160
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 160
          service:
            ports:
              - name: mainport
                port: 80
              - name: secport
                port: 5005
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: corp-platform-context
                service:
                  port:
                    number: 80
          configmap:
            data:
              - name: ADDITIONAL_OPTS
                value: ' '
              - name: PREFIX
                value: corp-platform-context
              - name: VAULT_HOST
                value: vault.prod.cvc.intra
              - name: VAULT_SCHEME
                value: http
              - name: CONSUL_HOST
                value: consul.prod.cvc.intra
              - name: CONSUL_PORT
                value: "8500"
              - name: ASPNETCORE_ENVIRONMENT
                value: prod
              - name: PROJECT_NAME
                value: sub-backend-flights
              - name: MongoDbSettings__ConnectionString
                value: "vault:secret/corp-platform-context#MONGO_HOST"
              - name: MongoDbSettings__DatabaseName
                value: "vault:secret/corp-platform-context#MONGO_NAME"
