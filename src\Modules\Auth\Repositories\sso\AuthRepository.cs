using System.Net;
using System.Net.Http.Headers;
    using System.Text.Json;
    using Auth.DTOs;

    namespace Auth.Repositories
    {
        public class AuthRepository : IAuthRepository
        {
            private readonly HttpClient _httpClient;
            private readonly IConfiguration _configuration;

            public AuthRepository(HttpClient httpClient, IConfiguration configuration)
            {
                _httpClient = httpClient;
                _configuration = configuration;
            }

            public async Task<TokenResponseDto> LoginAsync(string username, string password)
            {
                var baseUrl = _configuration["CorpAuth:BaseUrl:QA"];
                var endpoint = $"{baseUrl}v1/login/user?username={username}&password={password}";

                var request = new HttpRequestMessage(HttpMethod.Post, endpoint);
                request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                var response = await _httpClient.SendAsync(request);
                var content = await response.Content.ReadAsStringAsync();

                // Logar o conteúdo da resposta para diagnóstico
                Console.WriteLine($"Login Response Content: {content}");

                if (!response.IsSuccessStatusCode)
                {
                    // Tentar desserializar o corpo da resposta para obter detalhes do erro
                    try
                    {
                        var errorResponse = JsonSerializer.Deserialize<ErrorResponseDto>(content);
                        throw new HttpRequestException($"Erro: {response.StatusCode}, Código: {errorResponse?.ErrorCode}, Mensagem: {errorResponse?.Message}");
                    }
                    catch (JsonException)
                    {
                        // Caso a resposta não seja um JSON válido
                        throw new HttpRequestException($"Erro: {response.StatusCode}, Resposta: {content}");
                    }
                }

                return JsonSerializer.Deserialize<TokenResponseDto>(content);
            }

            public async Task<TokenResponseDto> GenerateAppTokenAsync(string ssoToken)
            {
                if (string.IsNullOrWhiteSpace(ssoToken))
                {
                    throw new ArgumentException("SSO Token cannot be null or empty.");
                }

                Console.WriteLine($"SSO Token Passed to GenerateAppToken: {ssoToken}");

                var baseUrl = _configuration["CorpAuth:BaseUrl:QA"];
                var applicationId = _configuration["CorpAuth:ApplicationId"];
                var endpoint = $"{baseUrl}v1/login/application?grant_type=implicit_corp&access_token={ssoToken}&client={applicationId}";

                Console.WriteLine($"GenerateAppToken Endpoint: {endpoint}");

                var request = new HttpRequestMessage(HttpMethod.Post, endpoint);
                request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                var response = await _httpClient.SendAsync(request);

                var content = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"GenerateAppToken Response Content: {content}");

                response.EnsureSuccessStatusCode();

                return JsonSerializer.Deserialize<TokenResponseDto>(content);
            }

            public async Task<bool> ValidateTokenAsync(string token)
            {
                var baseUrl = _configuration["CorpAuth:BaseUrl:QA"];
                var endpoint = $"{baseUrl}v1/token/validate-token?token={token}";

                try
                {
                    var request = new HttpRequestMessage(HttpMethod.Get, endpoint);
                    request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                    var response = await _httpClient.SendAsync(request);

                    if (!response.IsSuccessStatusCode)
                    {
                        return false;
                    }

                    var content = await response.Content.ReadAsStringAsync();
                    var validationResult = JsonSerializer.Deserialize<Dictionary<string, bool>>(content);

                    return validationResult.ContainsKey("active") && validationResult["active"];
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error during token validation: {ex.Message}");
                    return false;
                }
            }

            public async Task<TokenResponseDto> RefreshTokenAsync(string refreshToken)
            {
                if (string.IsNullOrWhiteSpace(refreshToken))
                {
                    throw new ArgumentException("Refresh token não pode ser vazio.");
                }

                var baseUrl = _configuration["CorpAuth:BaseUrl:QA"];
                var endpoint = $"{baseUrl}v1/token/refresh-token?refresh_token={refreshToken}";

                var request = new HttpRequestMessage(HttpMethod.Post, endpoint);
                request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                request.Headers.Authorization = new AuthenticationHeaderValue("Basic", _configuration["CorpAuth:ClientSecretBase64"]);

                var response = await _httpClient.SendAsync(request);
                var content = await response.Content.ReadAsStringAsync();

                // Logar o conteúdo da resposta para diagnóstico
                Console.WriteLine($"Refresh Token Response Content: {content}");

                if (!response.IsSuccessStatusCode)
                {
                    try
                    {
                        var errorResponse = JsonSerializer.Deserialize<ErrorResponseDto>(content);
                        throw new HttpRequestException($"Erro: {response.StatusCode}, Código: {errorResponse?.ErrorCode}, Mensagem: {errorResponse?.Message}");
                    }
                    catch (JsonException)
                    {
                        throw new HttpRequestException($"Erro: {response.StatusCode}, Resposta: {content}");
                    }
                }

                return JsonSerializer.Deserialize<TokenResponseDto>(content);
            }



        }
    }
