using System;
using System.Collections.Generic;
#nullable disable

namespace PlatformContext.Modules.Site.Dtos
{
    public class SiteDto
    {
        public string? Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string BrandId { get; set; }
        public string NavigationId { get; set; }
        public string HeaderId { get; set; }
        public bool IsActive { get; set; } // Propriedade adicionada
        public DateTime CreatedAt { get; set; } // Propriedade adicionada
        public DateTime UpdatedAt { get; set; } // Propriedade adicionada
    }
}