using System.Collections.Generic;
using System.Threading.Tasks;

namespace PlatformContext.Modules.Site
{
    public class ListAllSitesUseCase
    {
        private readonly ISiteRepository _siteRepository;

        public ListAllSitesUseCase(ISiteRepository siteRepository)
        {
            _siteRepository = siteRepository;
        }

        public async Task<(List<SiteWithDetails>, long)> ExecuteAsync(string? siteName, int pageNumber, int pageSize)
        {
            return await _siteRepository.GetAllAsync(siteName, pageNumber, pageSize);
        }
    }
}
