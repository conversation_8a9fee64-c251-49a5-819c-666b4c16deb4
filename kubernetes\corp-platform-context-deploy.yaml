apiVersion: apps/v1
kind: Deployment
metadata:
  name: corp-platform-context-deploy
  namespace: corp-platform
  labels:
    app: corp-platform-context
  annotations:
    vault.hashicorp.com/agent-inject: "true"
    vault.hashicorp.com/agent-inject-secret-MongoDbSettings: "secret/corp-platform-context"
    vault.hashicorp.com/agent-inject-template-MongoDbSettings: |
      {{- with secret "secret/corp-platform-context" -}}
      MongoDbSettings__TESTE: {{ .Data.TESTE }}
      MongoDbSettings__ConnectionString: {{ .Data.ConnectionString }}
      MongoDbSettings__DatabaseName: {{ .Data.DatabaseName }}
      {{- end }}

spec:
  replicas: 1
  selector:
    matchLabels:
      app: corp-platform-context
  template:
    metadata:
      labels:
        app: corp-platform-context
    spec:
      containers:
      - name: corp-platform-context
        image: 260584439167.dkr.ecr.sa-east-1.amazonaws.com/corp-platform-context:__TAG__
        imagePullPolicy: Always
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1024Mi"
            cpu: "500m"
        readinessProbe:
          failureThreshold: 3
          httpGet:
            path: /health
            port: 80
            httpHeaders:
            - name: X-Custom-Header
              value: ReadinessProbe
          initialDelaySeconds: 5
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 10
        livenessProbe:
          failureThreshold: 3
          httpGet:
            path: /health
            port: 80
            httpHeaders:
            - name: X-Custom-Header
              value: LivenessProbe
          initialDelaySeconds: 5
          periodSeconds: 15
          successThreshold: 1
          timeoutSeconds: 10
        envFrom:
          - configMapRef:
              name: corp-platform-context
          - secretRef:
              name: corp-platform-context
        env:
          - name: ASPNETCORE_ENVIRONMENT
            valueFrom:
              configMapKeyRef:
                name: corp-platform-context
                key: ASPNETCORE_ENVIRONMENT
          - name: MongoDbSettings__ConnectionString
            valueFrom:
              configMapKeyRef:
                name: corp-platform-context
                key: MongoDbSettings__ConnectionString
          - name: MongoDbSettings__DatabaseName
            valueFrom:
              configMapKeyRef:
                name: corp-platform-context
                key: MongoDbSettings__DatabaseName
          - name: PROJECT_NAME
            valueFrom:
              configMapKeyRef:
                name: corp-platform-context
                key: PROJECT_NAME
          - name: INSTANA_AGENT_HOST
            valueFrom:
              fieldRef:
                fieldPath: status.hostIP
        ports:
        - containerPort: 80
