using MongoDB.Driver;
using System.Collections.Generic;
using System.Threading.Tasks;
using MongoDB.Bson;
using PlatformContext.Modules.Headers;

namespace PlatformContext.Modules.Site
{
    public class SiteRepository : ISiteRepository
    {
        private readonly IMongoCollection<Site> _sitesCollection;
        private readonly IMongoCollection<Brand.Brand> _brandsCollection;
        private readonly IMongoCollection<Navigation.Navigation> _navigationsCollection;
        private readonly IMongoCollection<Header> _headersCollection;

        public SiteRepository(IMongoDatabase database)
        {
            _sitesCollection = database.GetCollection<Site>("Sites");
            _brandsCollection = database.GetCollection<Brand.Brand>("Brands");
            _navigationsCollection = database.GetCollection<Navigation.Navigation>("Navigations");
            _headersCollection = database.GetCollection<Header>("Headers");
        }

        public async Task<(List<SiteWithDetails>, long)> GetAllAsync(string? siteName, int pageNumber, int pageSize)
        {
            var filter = string.IsNullOrWhiteSpace(siteName)
                ? Builders<Site>.Filter.Empty
                : Builders<Site>.Filter.Regex("Name", new BsonRegularExpression(siteName, "i"));

            var totalRecords = await _sitesCollection.CountDocumentsAsync(filter);

            var sites = await _sitesCollection
                .Find(filter)
                .Skip((pageNumber - 1) * pageSize)
                .Limit(pageSize)
                .ToListAsync();

            var results = new List<SiteWithDetails>();

            foreach (var site in sites)
            {
                Brand.Brand? brand = null;
                Navigation.Navigation? navigation = null;
                Header? header = null;

                if (!string.IsNullOrWhiteSpace(site.BrandId) && ObjectId.TryParse(site.BrandId, out _))
                {
                    brand = await _brandsCollection.Find(b => b.Id == site.BrandId).FirstOrDefaultAsync();
                }

                if (!string.IsNullOrWhiteSpace(site.NavigationId) && ObjectId.TryParse(site.NavigationId, out _))
                {
                    navigation = await _navigationsCollection.Find(n => n.Id == site.NavigationId).FirstOrDefaultAsync();
                }

                if (!string.IsNullOrWhiteSpace(site.HeaderId) && ObjectId.TryParse(site.HeaderId, out _))
                {
                    header = await _headersCollection.Find(h => h.Id == site.HeaderId).FirstOrDefaultAsync();
                }

                site.Brand = brand;
                site.Navigation = navigation;
                site.Header = header;

                results.Add(new SiteWithDetails
                {
                    Site = site,
                });
            }

            return (results, totalRecords);
        }

        public async Task<Site> GetByIdAsync(string id)
        {
            var site = await _sitesCollection.Find(site => site.Id == id).FirstOrDefaultAsync();

            if (site != null)
            {
                if (!string.IsNullOrWhiteSpace(site.BrandId) && ObjectId.TryParse(site.BrandId, out _))
                {
                    site.Brand = await _brandsCollection.Find(b => b.Id == site.BrandId).FirstOrDefaultAsync();
                }

                if (!string.IsNullOrWhiteSpace(site.NavigationId) && ObjectId.TryParse(site.NavigationId, out _))
                {
                    site.Navigation = await _navigationsCollection.Find(n => n.Id == site.NavigationId).FirstOrDefaultAsync();
                }
            }

            return site;
        }

        public async Task CreateAsync(Site site)
        {
            await _sitesCollection.InsertOneAsync(site);
        }

        public async Task UpdateAsync(string id, Site updatedSite)
        {
            var updateDefinition = new List<UpdateDefinition<Site>>();
            
            if (!string.IsNullOrEmpty(updatedSite.Name))
                updateDefinition.Add(Builders<Site>.Update.Set(s => s.Name, updatedSite.Name));

            if (!string.IsNullOrEmpty(updatedSite.Description))
                updateDefinition.Add(Builders<Site>.Update.Set(s => s.Description, updatedSite.Description));

            if (!string.IsNullOrEmpty(updatedSite.BrandId))
                updateDefinition.Add(Builders<Site>.Update.Set(s => s.BrandId, updatedSite.BrandId));

            if (!string.IsNullOrEmpty(updatedSite.NavigationId))
                updateDefinition.Add(Builders<Site>.Update.Set(s => s.NavigationId, updatedSite.NavigationId));

            if (!string.IsNullOrEmpty(updatedSite.HeaderId))
                updateDefinition.Add(Builders<Site>.Update.Set(s => s.HeaderId, updatedSite.HeaderId));

            if (updateDefinition.Count > 0)
            {
                var update = Builders<Site>.Update.Combine(updateDefinition);
                await _sitesCollection.UpdateOneAsync(site => site.Id == id, update);
            }
        }


        public async Task DeleteAsync(string id)
        {
            await _sitesCollection.DeleteOneAsync(site => site.Id == id);
        }
    }

    public class SiteWithDetails
    {
        public Site Site { get; set; }
    }
}
