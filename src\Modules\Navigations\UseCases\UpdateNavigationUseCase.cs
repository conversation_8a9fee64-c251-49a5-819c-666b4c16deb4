using System.Threading.Tasks;
using PlatformContext.Modules.Navigation.Dtos;

namespace PlatformContext.Modules.Navigation
{
    public class UpdateNavigationUseCase
    {
        private readonly INavigationRepository _navigationRepository;

        public UpdateNavigationUseCase(INavigationRepository navigationRepository)
        {
            _navigationRepository = navigationRepository;
        }

        public async Task<Navigation> ExecuteAsync(string id, NavigationDto navigationDto)
        {
            var navigation = await _navigationRepository.GetByIdAsync(id);
            if (navigation == null)
            {
                throw new KeyNotFoundException($"Navegação com ID {id} não encontrada.");
            }

            navigation.Name = navigationDto.Name;
            navigation.Items = navigationDto.Items.ConvertAll(item => new NavigationItem
            {
                Label = item.Label,
                Url = item.Url,
                Icon = item.Icon,
                IsActive = item.IsActive
            });

            await _navigationRepository.UpdateAsync(id, navigation);
            return navigation;
        }
    }
}