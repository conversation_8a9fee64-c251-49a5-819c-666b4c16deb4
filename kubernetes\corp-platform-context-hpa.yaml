apiVersion: autoscaling/v2beta1
kind: HorizontalPodAutoscaler
metadata:
  name: corp-platform-context-hpa
  namespace: corp-platform
  labels:
    app: corp-platform-context
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: corp-platform-context-deploy
  minReplicas: 4
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      targetAverageUtilization: 160
  - type: Resource
    resource:
      name: memory
      targetAverageUtilization: 160
