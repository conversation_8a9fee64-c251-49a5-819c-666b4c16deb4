
using PlatformContext.Modules.Headers.Dtos;

namespace PlatformContext.Modules.Headers
{
    public class UpdateHeaderUseCase
    {
        private readonly IHeadersRepository _headersRepository;

        public UpdateHeaderUseCase(IHeadersRepository headersRepository)
        {
            _headersRepository = headersRepository;
        }

        public async Task ExecuteAsync(string id, HeadersDto headersDto)
        {
            if (string.IsNullOrWhiteSpace(id))
                throw new ArgumentException("Id cannot be null or empty.", nameof(id));

            var header = new Header
            {
                Id = headersDto.Id,
                Name = headersDto.Name,
                Headers = new HeaderItems
                {
                    Items = headersDto.Items.ConvertAll(itemDto => new Item
                    {
                        Id = itemDto.Id,
                        HeaderTitleMessages = itemDto.HeaderTitleMessages
                    })
                }
            };

            await _headersRepository.UpdateAsync(id, header);
        }
    }
}