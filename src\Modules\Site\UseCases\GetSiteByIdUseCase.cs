using System.Threading.Tasks;
using System.Collections.Generic;

namespace PlatformContext.Modules.Site
{
    public class GetSiteByIdUseCase
    {
        private readonly ISiteRepository _siteRepository;

        public GetSiteByIdUseCase(ISiteRepository siteRepository)
        {
            _siteRepository = siteRepository;
        }

        public async Task<Site> ExecuteAsync(string id)
        {
            var site = await _siteRepository.GetByIdAsync(id);
            if (site == null)
            {
                throw new KeyNotFoundException($"Site com ID {id} não encontrado.");
            }
            return site;
        }
    }
}