using System.Threading.Tasks;
using PlatformContext.Modules.Brand.Dtos;

namespace PlatformContext.Modules.Brand
{
    public class CreateBrandUseCase
    {
        private readonly IBrandRepository _brandRepository;

        public CreateBrandUseCase(IBrandRepository brandRepository)
        {
            _brandRepository = brandRepository;
        }
        
        public async Task<Brand> ExecuteAsync(BrandDto brandDto)
        {
            var brand = new Brand
            {
                Name = brandDto.Name,
                BrandType = brandDto.BrandType,
                Theme = new Theme
                {
                    ThemeName = brandDto.Theme.ThemeName,
                    Primary = brandDto.Theme.Primary,
                    Secondary = brandDto.Theme.Secondary
                },
                Logo = new Logo
                {
                    Dark = brandDto.Logo.Dark,
                    Light = brandDto.Logo.Light,
                    PreferredWidth = brandDto.Logo.PreferredWidth,
                    PreferredWidthMobile = brandDto.Logo.PreferredWidthMobile
                },
                Hosts = brandDto.Hosts ?? Array.Empty<string>(),
                Footer = new Footer
                {
                    Sections = brandDto.Footer.Sections?.ConvertAll(section => new FooterSection
                    {
                        Title = section.Title,
                        Items = section.Items?.ConvertAll(item => new FooterItem
                        {
                            Type = item.Type,
                            Text = item.Text,
                            Attributes = item.Attributes
                        }) ?? new List<FooterItem>()
                    }) ?? new List<FooterSection>(),
                    AppTitle = brandDto.Footer.AppTitle,
                    Apps = brandDto.Footer.Apps?.ConvertAll(app => new App
                    {
                        Name = app.Name,
                        Image = app.Image,
                        Url = app.Url
                    }) ?? new List<App>(),
                    SocialNetworksTitle = brandDto.Footer.SocialNetworksTitle,
                    SocialNetworks = brandDto.Footer.SocialNetworks?.ConvertAll(sn => new SocialNetwork
                    {
                        Name = sn.Name,
                        Icon = sn.Icon,
                        Attributes = sn.Attributes
                    }) ?? new List<SocialNetwork>(),
                    Payments = brandDto.Footer.Payments?.ConvertAll(payment => new Payment
                    {
                        Title = payment.Title,
                        Description = payment.Description,
                        Flags = payment.Flags?.ConvertAll(flag => new PaymentFlag
                        {
                            Name = flag.Name,
                            Icon = flag.Icon,
                            Description = flag.Description,
                        }) ?? new List<PaymentFlag>()
                    }) ?? new List<Payment>(),
                    ImportantReminders = brandDto.Footer.ImportantReminders?.ConvertAll(reminder => new ImportantReminder
                    {
                        Label = reminder.Label,
                        Description = reminder.Description
                    }) ?? new List<ImportantReminder>()
                },
                GtwData = new GtwData
                {
                    BranchId = brandDto.GtwData.BranchId,
                    AgentSign = brandDto.GtwData.AgentSign
                },
                GTM = brandDto.GTM,
                OmnilogicScript = brandDto.OmnilogicScript,
                ImageProcessorPath = brandDto.ImageProcessorPath,
                HeaderImageBg = new HeaderImageBg
                {
                    Desktop = brandDto.HeaderImageBg.Desktop,
                    Mobile = brandDto.HeaderImageBg.Mobile
                },
                CustomImageBgProduct = new CustomImageBgProduct
                {
                    ProductNames = brandDto.CustomImageBgProduct.ProductNames?.ToDictionary(
                        kvp => kvp.Key,
                        kvp => new ProductName
                        {
                            Desktop = kvp.Value.Desktop,
                            Mobile = kvp.Value.Mobile
                        }
                    ) ?? new Dictionary<string, ProductName>()
                },
                HomeMotorImageBg = brandDto.HomeMotorImageBg,
                Favicon = new FavIcon
                {
                    Small = brandDto.Favicon.Small,
                    Big = brandDto.Favicon.Big
                },
                HomeMessages = brandDto.HomeMessages ?? new Dictionary<string, string>(),
                HotjarIds = new HotjarIds
                {
                    MyTravelsFront = brandDto.HotjarIds.MyTravelsFront,
                    CustomerLoyalty = brandDto.HotjarIds.CustomerLoyalty
                },
                TermConditionLink = brandDto.TermConditionLink,
                PrivacyLink = brandDto.PrivacyLink,
                FeatureToggles = brandDto.FeatureToggles ?? Array.Empty<string>(),
                PendingPaymentContact = brandDto.PendingPaymentContact,
                ProductsRedirect = brandDto.ProductsRedirect?.ConvertAll(pr => new ProductRedirectInfo
                {
                    Name = pr.Name,
                    Url = pr.Url
                }) ?? new List<ProductRedirectInfo>(),
                RedirectsToLogin = brandDto.RedirectsToLogin ?? Array.Empty<string>()
            };

            await _brandRepository.CreateAsync(brand);
            return brand;
        }

    }
}
