{"_type": "export", "__export_format": 4, "__export_date": "2024-11-22T13:21:28.537Z", "__export_source": "insomnia.desktop.app:v9.2.0", "resources": [{"_id": "req_a0a04e293f4f46dba76c63cf1c160a8e", "parentId": "fld_416ccafad95547e69470de9a09417f68", "modified": 1731996316464, "created": 1731996293950, "url": "{{ _.baseURL }}/api/site/673c27c88b33d3a5059efb7b", "name": "DeleteSite", "description": "", "method": "DELETE", "body": {}, "preRequestScript": "", "parameters": [], "headers": [{"name": "User-Agent", "value": "insomnia/9.2.0"}], "authentication": {}, "metaSortKey": -1731996293950, "isPrivate": false, "pathParameters": [], "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "fld_416ccafad95547e69470de9a09417f68", "parentId": "wrk_a4157fe5d8284d1580a6e34ebf273d5b", "modified": 1731995213125, "created": 1731995213125, "name": "Site", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1731995213125, "_type": "request_group"}, {"_id": "wrk_a4157fe5d8284d1580a6e34ebf273d5b", "parentId": null, "modified": 1731900720012, "created": 1731900720012, "name": "Corp Context Platform", "description": "", "scope": "collection", "_type": "workspace"}, {"_id": "req_d370dd1037ce4f46b8fc8602b24d3fc2", "parentId": "fld_416ccafad95547e69470de9a09417f68", "modified": 1731996290478, "created": 1731996259150, "url": "{{ _.baseURL }}/api/site/673c27c88b33d3a5059efb7b", "name": "GetSiteById", "description": "", "method": "GET", "body": {}, "preRequestScript": "", "parameters": [], "headers": [{"name": "User-Agent", "value": "insomnia/9.2.0"}], "authentication": {}, "metaSortKey": -1731996259150, "isPrivate": false, "pathParameters": [], "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_9b2b2fdc4be647db93babcda77e39493", "parentId": "fld_416ccafad95547e69470de9a09417f68", "modified": 1731996246172, "created": 1731996183140, "url": "{{ _.baseURL }}/api/site/673c27c88b33d3a5059efb7b", "name": "UpdateSite", "description": "", "method": "PATCH", "body": {"mimeType": "application/json", "text": "{\n  \"name\": \"Site CVC Atualizado\",\n  \"domain\": \"www.cvc.com.br\",\n  \"description\": \"Site atualizado da CVC com novas informações sobre viagens.\",\n  \"supportedLanguages\": [\"pt-BR\", \"en-US\", \"es-ES\"],\n  \"pages\": [\n    {\n      \"title\": \"Home\",\n      \"url\": \"/\",\n      \"isActive\": true\n    },\n    {\n      \"title\": \"Contato\",\n      \"url\": \"/contato\",\n      \"isActive\": true\n    }\n  ],\n  \"featureToggles\": [\n    {\n      \"featureName\": \"dark-mode\",\n      \"isEnabled\": false\n    }\n  ],\n  \"contactInformation\": {\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"+55 11 4002-1234\",\n    \"address\": \"Av. das Américas, 54321, Rio de Janeiro\"\n  },\n  \"brandId\": \"673abf62496784e2f9c60416\" \n}\n"}, "preRequestScript": "", "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json"}, {"name": "User-Agent", "value": "insomnia/9.2.0"}], "authentication": {}, "metaSortKey": -1731996183140, "isPrivate": false, "pathParameters": [], "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_e5c41f8fe9ab4b08bf0c971cc78f965c", "parentId": "fld_416ccafad95547e69470de9a09417f68", "modified": 1731996632987, "created": 1731996146916, "url": "{{ _.baseURL }}/api/site", "name": "FindAllSites", "description": "", "method": "GET", "body": {}, "preRequestScript": "", "parameters": [{"id": "pair_e459ba81d76b414b968f8fbf00398812", "name": "pageNumber", "value": "1", "description": ""}, {"id": "pair_84bf2373ea114957b24780712c72c624", "name": "pageSize", "value": "10", "description": ""}], "headers": [{"name": "User-Agent", "value": "insomnia/9.2.0"}], "authentication": {}, "metaSortKey": -1731996146916, "isPrivate": false, "pathParameters": [], "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_a9f7dfc583c648c6bb6422d809534c11", "parentId": "fld_416ccafad95547e69470de9a09417f68", "modified": 1731995559287, "created": 1731995216418, "url": "{{ _.baseURL }}/api/site", "name": "CreateSite", "description": "", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n    \"name\": \"My Test Site\",\n    \"domain\": \"www.testsite.com\",\n    \"description\": \"This is a test site for demonstration purposes.\",\n    \"supportedLanguages\": [\"en\", \"es\", \"pt\"],\n    \"pages\": [\n        {\n            \"title\": \"Home\",\n            \"url\": \"/\",\n            \"isActive\": true\n        },\n        {\n            \"title\": \"Contact\",\n            \"url\": \"/contact\",\n            \"isActive\": true\n        }\n    ],\n    \"featureToggles\": [\n        {\n            \"featureName\": \"New Layout\",\n            \"isEnabled\": true\n        },\n        {\n            \"featureName\": \"Beta Feature\",\n            \"isEnabled\": false\n        }\n    ],\n    \"contactInformation\": {\n        \"email\": \"<EMAIL>\",\n        \"phone\": \"******-1234\",\n        \"address\": \"123 Test Street, Test City, TC\"\n    },\n    \"brandId\": \"673ac09ee167922b6ebfb839\"\n}\n"}, "preRequestScript": "", "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json"}, {"name": "User-Agent", "value": "insomnia/9.2.0"}], "authentication": {}, "metaSortKey": -1731995216418, "isPrivate": false, "pathParameters": [], "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_40efeb1f74d84e2b88f1aa32048f8507", "parentId": "fld_a857a848319f464988f0626ffe52fa51", "modified": 1731995310832, "created": 1731905571751, "url": "{{ _.baseURL }}/api/brand", "name": "FindAllBrands", "description": "", "method": "GET", "body": {}, "preRequestScript": "", "parameters": [{"id": "pair_27ecf22ac18048d78d2ad6e3cb8ad828", "name": "pageNumber", "value": "1", "description": ""}, {"id": "pair_090f98a2a0d74dcfb7f069edd45be862", "name": "pageSize", "value": "10", "description": ""}], "headers": [{"name": "User-Agent", "value": "insomnia/9.2.0"}], "authentication": {}, "metaSortKey": -1731905571751, "isPrivate": false, "pathParameters": [], "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "fld_a857a848319f464988f0626ffe52fa51", "parentId": "wrk_a4157fe5d8284d1580a6e34ebf273d5b", "modified": 1731900728610, "created": 1731900728610, "name": "Brands", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1731900728610, "_type": "request_group"}, {"_id": "req_74b1334515fe4c588dc9a97ad2f72c33", "parentId": "fld_a857a848319f464988f0626ffe52fa51", "modified": 1731905972193, "created": 1731905099672, "url": "{{ _.baseURL }}/api/brand/673ac614de03df551f75b0fd", "name": "<PERSON>ete<PERSON>rand", "description": "", "method": "DELETE", "body": {}, "preRequestScript": "", "parameters": [], "headers": [{"name": "User-Agent", "value": "insomnia/9.2.0"}], "authentication": {}, "metaSortKey": -1731905099672, "isPrivate": false, "pathParameters": [], "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_d173f0dc62fd41f896a8b39d86285fd3", "parentId": "fld_a857a848319f464988f0626ffe52fa51", "modified": 1731905933085, "created": 1731905068793, "url": "{{ _.baseURL }}/api/brand/673ac614de03df551f75b0fd", "name": "UpdateBrand", "description": "", "method": "PATCH", "body": {"mimeType": "application/json", "text": "{\n  \"name\": \"SUB\",\n  \"brandType\": \"Viagens C\",\n  \"theme\": {\n    \"themeName\": \"Azul e Amarela\",\n    \"primary\": \"#0054a6\",\n    \"secondary\": \"#ffcc00\"\n  },\n  \"logo\": {\n    \"dark\": \"/assets/logos/cvc-dark.png\",\n    \"light\": \"/assets/logos/cvc-light.png\",\n    \"preferredWidth\": \"200px\",\n    \"preferredWidthMobile\": \"150px\"\n  },\n  \"hosts\": [\n    \"www.cvc.com.br\",\n    \"cvc.com.br\"\n  ],\n  \"menu\": {\n    \"principals\": [\n      {\n        \"product\": \"Pacotes\",\n        \"label\": \"Pacotes de Viagem\",\n        \"link\": \"/pacotes\",\n        \"target\": \"_self\",\n        \"icon\": \"icon-pacotes\",\n        \"type\": \"main\",\n        \"id\": \"menu-pacotes\"\n      },\n      {\n        \"product\": \"Hotéis\",\n        \"label\": \"Hotéis\",\n        \"link\": \"/hoteis\",\n        \"target\": \"_self\",\n        \"icon\": \"icon-hoteis\",\n        \"type\": \"main\",\n        \"id\": \"menu-hoteis\"\n      }\n    ],\n    \"others\": [\n      {\n        \"product\": \"Carros\",\n        \"label\": \"<PERSON><PERSON><PERSON>\",\n        \"link\": \"/carros\",\n        \"target\": \"_self\",\n        \"icon\": \"icon-carros\",\n        \"type\": \"secondary\",\n        \"id\": \"menu-carros\"\n      }\n    ],\n    \"phone\": \"+55 11 3003-9282\",\n    \"phoneSales\": \"+55 11 4004-0000\",\n    \"help\": \"/ajuda\",\n    \"register\": \"/cadastro\",\n    \"login\": \"/login\"\n  },\n  \"navigation\": {\n    \"items\": [\n      {\n        \"id\": \"nav-home\",\n        \"label\": \"Início\",\n        \"href\": \"/\",\n        \"icon\": \"icon-home\"\n      },\n      {\n        \"id\": \"nav-promocoes\",\n        \"label\": \"Promoções\",\n        \"href\": \"/promocoes\",\n        \"icon\": \"icon-promocoes\"\n      }\n    ]\n  },\n  \"footer\": {\n    \"sections\": [\n      {\n        \"title\": \"Institucional\",\n        \"items\": [\n          {\n            \"type\": \"link\",\n            \"text\": \"Sobre a CVC\",\n            \"attributes\": {\n              \"href\": \"/sobre\",\n              \"target\": \"_self\"\n            }\n          },\n          {\n            \"type\": \"link\",\n            \"text\": \"Trabalhe Conosco\",\n            \"attributes\": {\n              \"href\": \"/trabalhe-conosco\",\n              \"target\": \"_self\"\n            }\n          }\n        ]\n      }\n    ],\n    \"appTitle\": \"Baixe nosso app\",\n    \"apps\": [\n      {\n        \"name\": \"Google Play\",\n        \"image\": \"/assets/apps/google-play.png\",\n        \"url\": \"https://play.google.com/store\"\n      },\n      {\n        \"name\": \"App Store\",\n        \"image\": \"/assets/apps/app-store.png\",\n        \"url\": \"https://www.apple.com/app-store/\"\n      }\n    ],\n    \"socialNetworksTitle\": \"Siga-nos\",\n    \"socialNetworks\": [\n      {\n        \"name\": \"Facebook\",\n        \"icon\": \"icon-facebook\",\n        \"attributes\": {\n          \"href\": \"https://www.facebook.com/cvc\"\n        }\n      },\n      {\n        \"name\": \"Instagram\",\n        \"icon\": \"icon-instagram\",\n        \"attributes\": {\n          \"href\": \"https://www.instagram.com/cvc\"\n        }\n      }\n    ],\n    \"payments\": [\n      {\n        \"title\": \"Cartões de Crédito\",\n        \"flags\": [\n          {\n            \"name\": \"Visa\",\n            \"icon\": \"icon-visa\"\n          },\n          {\n            \"name\": \"MasterCard\",\n            \"icon\": \"icon-mastercard\"\n          }\n        ]\n      }\n    ],\n    \"importantReminders\": [\n      {\n        \"label\": \"Aviso Importante\",\n        \"description\": \"Verifique as condições de cada serviço antes de finalizar a compra.\"\n      }\n    ]\n  },\n  \"gtwData\": {\n    \"branchId\": \"12345\",\n    \"agentSign\": \"AGENT123\"\n  },\n  \"gtm\": \"GTM-XXXXXX\",\n  \"omnilogicScript\": \"console.log('Omnilogic script ativo');\",\n  \"imageProcessorPath\": \"/assets/image-processor\",\n  \"headerImageBg\": {\n    \"desktop\": \"/assets/images/header-desktop.jpg\",\n    \"mobile\": \"/assets/images/header-mobile.jpg\"\n  },\n  \"customImageBgProduct\": {\n    \"productNames\": {\n      \"Viagem\": {\n        \"desktop\": \"/assets/images/viagem-desktop.jpg\",\n        \"mobile\": \"/assets/images/viagem-mobile.jpg\"\n      }\n    }\n  },\n  \"homeMotorImageBg\": \"/assets/images/home-motor.jpg\",\n  \"favicon\": {\n    \"small\": \"/assets/icons/favicon-small.ico\",\n    \"big\": \"/assets/icons/favicon-big.ico\"\n  },\n  \"homeMessages\": {\n    \"welcome\": \"Bem-vindo à CVC!\",\n    \"promo\": \"Confira nossas promoções exclusivas.\"\n  },\n  \"hotjarIds\": {\n    \"myTravelsFront\": \"HJ-123456\",\n    \"customerLoyalty\": \"HJ-654321\"\n  },\n  \"termConditionLink\": \"/termos-e-condicoes\",\n  \"privacyLink\": \"/politica-de-privacidade\",\n  \"featureToggles\": [\n    \"novo-layout\",\n    \"promo-destaque\"\n  ],\n  \"pendingPaymentContact\": \"<EMAIL>\",\n  \"productsRedirect\": [\n    {\n      \"name\": \"Pacote para Nordeste\",\n      \"url\": \"/pacotes/nordeste\"\n    }\n  ],\n  \"redirectsToLogin\": [\n    \"/minha-conta\",\n    \"/reservas\"\n  ]\n}\n"}, "preRequestScript": "", "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json"}, {"name": "User-Agent", "value": "insomnia/9.2.0"}], "authentication": {}, "metaSortKey": -1731905068793, "isPrivate": false, "pathParameters": [], "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_cc1212d4684b40dfacf3e7e957de7249", "parentId": "fld_a857a848319f464988f0626ffe52fa51", "modified": 1731905917475, "created": 1731904964971, "url": "{{ _.baseURL }}/api/brand/673ac614de03df551f75b0fd", "name": "GetBrandById", "description": "", "method": "GET", "body": {}, "preRequestScript": "", "parameters": [], "headers": [{"name": "User-Agent", "value": "insomnia/9.2.0"}], "authentication": {}, "metaSortKey": -1731904964971, "isPrivate": false, "pathParameters": [], "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_3ae3c0cecded4f7993436fdc51286ec9", "parentId": "fld_a857a848319f464988f0626ffe52fa51", "modified": 1731905042029, "created": 1731900731263, "url": "{{ _.baseURL }}/api/brand", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n  \"name\": \"SUB\",\n  \"brandType\": \"Viagens\",\n  \"theme\": {\n    \"themeName\": \"Azul e Branco\",\n    \"primary\": \"#0054a6\",\n    \"secondary\": \"#ffcc00\"\n  },\n  \"logo\": {\n    \"dark\": \"/assets/logos/cvc-dark.png\",\n    \"light\": \"/assets/logos/cvc-light.png\",\n    \"preferredWidth\": \"200px\",\n    \"preferredWidthMobile\": \"150px\"\n  },\n  \"hosts\": [\n    \"www.cvc.com.br\",\n    \"cvc.com.br\"\n  ],\n  \"menu\": {\n    \"principals\": [\n      {\n        \"product\": \"Pacotes\",\n        \"label\": \"Pacotes de Viagem\",\n        \"link\": \"/pacotes\",\n        \"target\": \"_self\",\n        \"icon\": \"icon-pacotes\",\n        \"type\": \"main\",\n        \"id\": \"menu-pacotes\"\n      },\n      {\n        \"product\": \"Hotéis\",\n        \"label\": \"Hotéis\",\n        \"link\": \"/hoteis\",\n        \"target\": \"_self\",\n        \"icon\": \"icon-hoteis\",\n        \"type\": \"main\",\n        \"id\": \"menu-hoteis\"\n      }\n    ],\n    \"others\": [\n      {\n        \"product\": \"Carros\",\n        \"label\": \"<PERSON><PERSON><PERSON>\",\n        \"link\": \"/carros\",\n        \"target\": \"_self\",\n        \"icon\": \"icon-carros\",\n        \"type\": \"secondary\",\n        \"id\": \"menu-carros\"\n      }\n    ],\n    \"phone\": \"+55 11 3003-9282\",\n    \"phoneSales\": \"+55 11 4004-0000\",\n    \"help\": \"/ajuda\",\n    \"register\": \"/cadastro\",\n    \"login\": \"/login\"\n  },\n  \"navigation\": {\n    \"items\": [\n      {\n        \"id\": \"nav-home\",\n        \"label\": \"Início\",\n        \"href\": \"/\",\n        \"icon\": \"icon-home\"\n      },\n      {\n        \"id\": \"nav-promocoes\",\n        \"label\": \"Promoções\",\n        \"href\": \"/promocoes\",\n        \"icon\": \"icon-promocoes\"\n      }\n    ]\n  },\n  \"footer\": {\n    \"sections\": [\n      {\n        \"title\": \"Institucional\",\n        \"items\": [\n          {\n            \"type\": \"link\",\n            \"text\": \"Sobre a CVC\",\n            \"attributes\": {\n              \"href\": \"/sobre\",\n              \"target\": \"_self\"\n            }\n          },\n          {\n            \"type\": \"link\",\n            \"text\": \"Trabalhe Conosco\",\n            \"attributes\": {\n              \"href\": \"/trabalhe-conosco\",\n              \"target\": \"_self\"\n            }\n          }\n        ]\n      }\n    ],\n    \"appTitle\": \"Baixe nosso app\",\n    \"apps\": [\n      {\n        \"name\": \"Google Play\",\n        \"image\": \"/assets/apps/google-play.png\",\n        \"url\": \"https://play.google.com/store\"\n      },\n      {\n        \"name\": \"App Store\",\n        \"image\": \"/assets/apps/app-store.png\",\n        \"url\": \"https://www.apple.com/app-store/\"\n      }\n    ],\n    \"socialNetworksTitle\": \"Siga-nos\",\n    \"socialNetworks\": [\n      {\n        \"name\": \"Facebook\",\n        \"icon\": \"icon-facebook\",\n        \"attributes\": {\n          \"href\": \"https://www.facebook.com/cvc\"\n        }\n      },\n      {\n        \"name\": \"Instagram\",\n        \"icon\": \"icon-instagram\",\n        \"attributes\": {\n          \"href\": \"https://www.instagram.com/cvc\"\n        }\n      }\n    ],\n    \"payments\": [\n      {\n        \"title\": \"Cartões de Crédito\",\n        \"flags\": [\n          {\n            \"name\": \"Visa\",\n            \"icon\": \"icon-visa\"\n          },\n          {\n            \"name\": \"MasterCard\",\n            \"icon\": \"icon-mastercard\"\n          }\n        ]\n      }\n    ],\n    \"importantReminders\": [\n      {\n        \"label\": \"Aviso Importante\",\n        \"description\": \"Verifique as condições de cada serviço antes de finalizar a compra.\"\n      }\n    ]\n  },\n  \"gtwData\": {\n    \"branchId\": \"12345\",\n    \"agentSign\": \"AGENT123\"\n  },\n  \"gtm\": \"GTM-XXXXXX\",\n  \"omnilogicScript\": \"console.log('Omnilogic script ativo');\",\n  \"imageProcessorPath\": \"/assets/image-processor\",\n  \"headerImageBg\": {\n    \"desktop\": \"/assets/images/header-desktop.jpg\",\n    \"mobile\": \"/assets/images/header-mobile.jpg\"\n  },\n  \"customImageBgProduct\": {\n    \"productNames\": {\n      \"Viagem\": {\n        \"desktop\": \"/assets/images/viagem-desktop.jpg\",\n        \"mobile\": \"/assets/images/viagem-mobile.jpg\"\n      }\n    }\n  },\n  \"homeMotorImageBg\": \"/assets/images/home-motor.jpg\",\n  \"favicon\": {\n    \"small\": \"/assets/icons/favicon-small.ico\",\n    \"big\": \"/assets/icons/favicon-big.ico\"\n  },\n  \"homeMessages\": {\n    \"welcome\": \"Bem-vindo à CVC!\",\n    \"promo\": \"Confira nossas promoções exclusivas.\"\n  },\n  \"hotjarIds\": {\n    \"myTravelsFront\": \"HJ-123456\",\n    \"customerLoyalty\": \"HJ-654321\"\n  },\n  \"termConditionLink\": \"/termos-e-condicoes\",\n  \"privacyLink\": \"/politica-de-privacidade\",\n  \"featureToggles\": [\n    \"novo-layout\",\n    \"promo-destaque\"\n  ],\n  \"pendingPaymentContact\": \"<EMAIL>\",\n  \"productsRedirect\": [\n    {\n      \"name\": \"Pacote para Nordeste\",\n      \"url\": \"/pacotes/nordeste\"\n    }\n  ],\n  \"redirectsToLogin\": [\n    \"/minha-conta\",\n    \"/reservas\"\n  ]\n}\n"}, "preRequestScript": "", "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json"}, {"name": "User-Agent", "value": "insomnia/9.2.0"}], "authentication": {}, "metaSortKey": -1731900731263, "isPrivate": false, "pathParameters": [], "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "env_0c7e71949ed80ee8df91e5c44f2fef083c37fb0c", "parentId": "wrk_a4157fe5d8284d1580a6e34ebf273d5b", "modified": 1731900768844, "created": 1731900720015, "name": "Base Environment", "data": {"baseURL": "http://localhost:5000"}, "dataPropertyOrder": {"&": ["baseURL"]}, "color": null, "isPrivate": false, "metaSortKey": 1731900720015, "_type": "environment"}, {"_id": "jar_0c7e71949ed80ee8df91e5c44f2fef083c37fb0c", "parentId": "wrk_a4157fe5d8284d1580a6e34ebf273d5b", "modified": 1731900720017, "created": 1731900720017, "name": "<PERSON><PERSON><PERSON>", "cookies": [], "_type": "cookie_jar"}]}