using System.Diagnostics;
using System.IO;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using PlatformContext.Domain.Models;

namespace PlatformContext.Middlewares
{
    public class LoggingInterceptorMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<LoggingInterceptorMiddleware> _logger;
        private readonly IMongoCollection<RequestLog> _logCollection;

        public LoggingInterceptorMiddleware(RequestDelegate next, ILogger<LoggingInterceptorMiddleware> logger, IMongoClient mongoClient)
        {
            _next = next;
            _logger = logger;
            var database = mongoClient.GetDatabase("PlatformContextLogs");
            _logCollection = database.GetCollection<RequestLog>("RequestLogs");
        }

        public async Task InvokeAsync(HttpContext context)
        {
            var stopwatch = Stopwatch.StartNew();

            var originalResponseBodyStream = context.Response.Body;
            await using var responseBodyStream = new MemoryStream();
            context.Response.Body = responseBodyStream;

            var requestBody = await ReadRequestBodyAsync(context.Request);

            await _next(context);

            var responseBody = await ReadResponseBodyAsync(responseBodyStream);
            stopwatch.Stop();

            var logEntry = new RequestLog
            {
                Method = context.Request.Method,
                Path = context.Request.Path,
                QueryString = context.Request.QueryString.ToString(),
                RequestBody = requestBody,
                ResponseBody = responseBody,
                StatusCode = context.Response.StatusCode,
                ProcessingTimeMs = stopwatch.ElapsedMilliseconds,
                Timestamp = DateTime.UtcNow
            };

            await SaveLogToDatabaseAsync(logEntry);
            _logger.LogInformation(JsonSerializer.Serialize(logEntry));

            responseBodyStream.Seek(0, SeekOrigin.Begin);
            await responseBodyStream.CopyToAsync(originalResponseBodyStream);
        }

        private static async Task<string> ReadRequestBodyAsync(HttpRequest request)
        {
            if (request.ContentLength == null || !(request.ContentLength > 0) || !request.Body.CanSeek)
                return null;

            request.EnableBuffering();
            var buffer = new byte[request.ContentLength.Value];
            await request.Body.ReadAsync(buffer.AsMemory(0, buffer.Length));
            request.Body.Seek(0, SeekOrigin.Begin);

            return Encoding.UTF8.GetString(buffer);
        }

        private static async Task<string> ReadResponseBodyAsync(Stream responseBodyStream)
        {
            responseBodyStream.Seek(0, SeekOrigin.Begin);
            using var reader = new StreamReader(responseBodyStream, Encoding.UTF8);
            var text = await reader.ReadToEndAsync();
            responseBodyStream.Seek(0, SeekOrigin.Begin);

            return text;
        }

        private async Task SaveLogToDatabaseAsync(RequestLog logEntry)
        {
            await _logCollection.InsertOneAsync(logEntry);
        }
    }

    public static class LoggingInterceptorMiddlewareExtensions
    {
        public static IApplicationBuilder UseLoggingInterceptor(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<LoggingInterceptorMiddleware>();
        }
    }
}
