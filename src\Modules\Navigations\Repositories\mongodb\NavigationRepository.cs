using MongoDB.Driver;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PlatformContext.Modules.Navigation
{
    public class NavigationRepository : INavigationRepository
    {
        private readonly IMongoCollection<Navigation> _navigationsCollection;

        public NavigationRepository(IMongoDatabase database)
        {
            _navigationsCollection = database.GetCollection<Navigation>("Navigations");
        }

        public async Task<(List<Navigation>, int)> GetAllAsync(int page, int pageSize)
        {
            var totalDocuments = await _navigationsCollection.CountDocumentsAsync(FilterDefinition<Navigation>.Empty);
            var navigations = await _navigationsCollection
                .Find(FilterDefinition<Navigation>.Empty)
                .Skip((page - 1) * pageSize)
                .Limit(pageSize)
                .ToListAsync();

            return (navigations, (int)totalDocuments);
        }

        public async Task<Navigation> GetByIdAsync(string id)
        {
            return await _navigationsCollection.Find(nav => nav.Id == id).FirstOrDefaultAsync();
        }

        public async Task CreateAsync(Navigation navigation)
        {
            await _navigationsCollection.InsertOneAsync(navigation);
        }

        public async Task UpdateAsync(string id, Navigation updatedNavigation)
        {
            await _navigationsCollection.ReplaceOneAsync(nav => nav.Id == id, updatedNavigation);
        }

        public async Task DeleteAsync(string id)
        {
            await _navigationsCollection.DeleteOneAsync(nav => nav.Id == id);
        }
    }
}