﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PlatformContext.Services.Model
{
   public class BrandContext
{
    public string Id { get; set; }
    public string Name { get; set; }
    public string Brand { get; set; }
    public string[] Hosts { get; set; }
    public Theme Theme { get; set; }
    public MenuType Menu { get; set; }
    public Navigation Navigation { get; set; } = new Navigation();
    public Headers Headers { get; set; } = new Headers();
    public FooterType Footer { get; set; }
    public Logo Logo { get; set; }
    public GtwData GtwData { get; set; }
    public string GTM { get; set; }
    public string OmnilogicScript { get; set; }
    public string ImageProcessorPath { get; set; }
    public HeaderImageBg HeaderImageBg { get; set; }
    public CustomImageBgProduct CustomImageBgProduct { get; set; }
    public string HomeMotorImageBg { get; set; }
    public FavIcon Favicon { get; set; }
    public string Compl { get; set; }
    public Dictionary<string, string> HomeMessages { get; set; }
    public HotjarIds HotjarIds { get; set; }
    public string TermConditionLink { get; set; }
    public string PrivacityLink { get; set; }
    public string[] FeatureToggles { get; set; }
    public string PendingPaymentContact { get; set; }

    public ProductRedirectInfo[] ProductsRedirect { get; set; }
    public string[] RedirectsToLogin { get; set; }
}

    public class CustomImageBgProduct
    {
        public Dictionary<string, ProductName> productNames { get; set; }
    }

    public class ProductRedirectInfo
    {
        public string Name { get; set; }
        public string Url { get; set; }
    }

    public class ProductName
    {
        public string Desktop { get; set; }
        public string Mobile { get; set; }
    }

    public class HotjarIds
    {
        public string MyTravelsFront { get; set; }
        public string CustomerLoyalty { get; set; }   
    }

    public class HeaderImageBg
    {
        public string Desktop { get; set; }
        public string Mobile { get; set; }
    }
    public class FavIcon
    {
        public string Small { get; set; }
        public string Big { get; set; }
    }
    public class Logo
    {
        public string Dark { get; set; }
        public string Light { get; set; }
        public string PreferredWidth { get; set; }
        public string PreferredWidthMobile { get; set; }
    }
    public class GtwData
    {
        public string BranchId { get; set; }
        public string AgentSign { get; set; }
    }
    public class Theme
    {
        public string ThemeName { get; set; }
        public string Air { get; set; }
        public string Alert { get; set; }
        public string Approved { get; set; }
        public string Backdrop { get; set; }
        public string Backdrop70 { get; set; }
        public string Backdrop100 { get; set; }
        public string Border { get; set; }
        public string Canceled { get; set; }
        public string Car { get; set; }
        public string ContrastSecondaryText { get; set; }
        public string Disabled { get; set; }
        public string Error { get; set; }
        public string Hotel { get; set; }
        public string Offwhite { get; set; }
        public string PackageColor { get; set; }
        public string Primary { get; set; }
        public string PrimaryAlpha { get; set; }
        public string PrimaryAlphaDark { get; set; }
        public string PrimaryDark { get; set; }
        public string PrimaryDarkHover { get; set; }
        public string PrimaryLight { get; set; }
        public string PrimaryLighter { get; set; }
        public string PrimaryText { get; set; }
        public string Processed { get; set; }
        public string Secondary { get; set; }
        public string SecondaryDark { get; set; }
        public string SecondaryHover { get; set; }
        public string SecondaryLighter { get; set; }
        public string SecondaryText { get; set; }
        public string Spot { get; set; }
        public string Success { get; set; }
        public string SuccessHover { get; set; }
        public string Tabs { get; set; }
        public string TabsHover { get; set; }
        public string White { get; set; }
        public string White30 { get; set; }
        public string Regular { get; set; }
        public string Bold { get; set; }
    }

    public class MenuType
    {
        public Item[] HiddenPrincipals { get; set; }
        public Item[] Principals { get; set; }
        public Item[] HiddenOthers { get; set; }
        public Item[] Others { get; set; }
        public string Phone { get; set; }
        public string PhoneSales { get; set; }
        public string Help { get; set; }
        public string Register { get; set; }
        public string Login { get; set; }

        public class Item
        {
            public string Product { get; set; }
            public string Label { get; set; }
            public string Link { get; set; }
            public string Target { get; set; }
            public string Icon { get; set; }
            public string Type { get; set; }
            public string Id { get; set; }
        }
    }

    public class FooterType
    {
        public Section[] Sections { get; set; }
        public string AppTitle { get; set; }
        public App[] Apps { get; set; }
        public string SocialNetworksTitle { get; set; }
        public SocialNetwork[] SocialNetworks { get; set; }
        public Payment[] Payments { get; set; }
        public PaymentV2[] PaymentsV2 { get; set; }
        public ImportantReminderProps[] ImportantReminder { get; set; }
        public TextFooter[] Texts { get; set; }

        public class App
        {
            public string Name { get; set; }
            public string Image { get; set; }
            public string Url { get; set; }
        }
        public class TextFooter
        {
            public string Text { get; set; }
            public string TextOnBreakMobile { get; set; }
        }
        public class Payment
        {
            public string Title { get; set; }
            public Flag[] Flags { get; set; }

            public class Flag
            {
                public string Name { get; set; }
                public string Icon { get; set; }
            }
        }

        public class PaymentV2
        {
            public string Title { get; set; }
            public Option[] Options { get; set; }

            public class Option
            {
                public string Title { get; set; }
                public string? Description { get; set; }  
                public Flag[] Flags { get; set; }
            }

            public class Flag
            {
                public string Name { get; set; }
                public string Icon { get; set; }
                public string? Description { get; set; } 
                public string? Type { get; set; }
            }
        }

        public class ImportantReminderProps
        {
            public string Label { get; set; }
            public string Description { get; set; }
        }


        public class Section
        {
            public string Title { get; set; }
            public Item[] Items { get; set; }

            public class Item
            {
                public string Type { get; set; }
                public string Text { get; set; }
                public Dictionary<string, string> Attributes { get; set; }
            }
        }

        public class SocialNetwork
        {
            public string Name { get; set; }
            public string Icon { get; set; }
            public Dictionary<string, string> Attributes { get; set; }
        }
    }
}