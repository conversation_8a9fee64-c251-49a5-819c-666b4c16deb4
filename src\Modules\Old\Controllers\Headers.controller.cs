using System.Linq;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using PlatformContext.Services;
using PlatformContext.Services.Model;

namespace PlatformContext.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class HeadersController : ControllerBase
    {
        private readonly ILogger<HeadersController> _logger;

        public HeadersController(ILogger<HeadersController> logger)
        {
            _logger = logger;
        }

        [HttpGet]
         public ActionResult<HeadersResponse> Get([FromQuery] string brand)
        {
            if (string.IsNullOrEmpty(brand))
            {
                return BadRequest("O parâmetro 'brand' é obrigatório.");
            }

            var brandContext = Data.Brands.FirstOrDefault(b => b.Id.Equals(brand, System.StringComparison.OrdinalIgnoreCase));
            if (brandContext == null)
            {
                return NotFound($"Brand '{brand}' não encontrada.");
            }

            var items = Data.Headers.Items.Select(item => new HeaderItem
            {
                Id = item.Id ?? null,
                Label = item.Label,
                Href = item.Href,
                Icon = item.Icon,
                HeaderTitleMessages = item.HeaderTitleMessages
               
            }).ToList();

            var headersResponse = new HeadersResponse
            {
                Headers = new Headers
                {
                    Items = items
                }
            };

            return Ok(headersResponse);
        }
    }
}
