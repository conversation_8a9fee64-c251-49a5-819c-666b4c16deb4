using System;

namespace PlatformContext.Domain.Models
{
    public class RequestLog
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Path { get; set; }
        public string Method { get; set; }
        public string QueryString { get; set; }
        public object RequestBody { get; set; }
        public object ResponseBody { get; set; }
        public int StatusCode { get; set; }
        public long ProcessingTimeMs { get; set; } 
        public DateTime Timestamp { get; set; }
    }
}
