using System.Collections.Generic;
using System.Threading.Tasks;

namespace PlatformContext.Modules.Site
{
    public interface ISiteRepository
    {
        Task<(List<SiteWithDetails>, long)> GetAllAsync(string? siteName, int pageNumber, int pageSize);
        Task<Site> GetByIdAsync(string id);
        Task CreateAsync(Site site);
        Task UpdateAsync(string id, Site updatedSite);
        Task DeleteAsync(string id);
    }
}
