{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\corp-platform-context\\src\\PlatformContext.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\corp-platform-context\\src\\PlatformContext.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\corp-platform-context\\src\\PlatformContext.csproj", "projectName": "corp-platform-context", "projectPath": "C:\\Users\\<USER>\\source\\repos\\corp-platform-context\\src\\PlatformContext.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\corp-platform-context\\src\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"DotNetEnv": {"target": "Package", "version": "[3.1.1, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[6.0.20, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[9.0.0, )"}, "MongoDB.Driver": {"target": "Package", "version": "[3.0.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[7.1.0, )"}, "Swashbuckle.AspNetCore.Annotations": {"target": "Package", "version": "[6.5.0, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.31, 6.0.31]"}, {"name": "Microsoft.NETCore.App.Host.win-x86", "version": "[6.0.31, 6.0.31]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.31, 6.0.31]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.31, 6.0.31]"}], "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files (x86)\\dotnet\\sdk\\7.0.410\\RuntimeIdentifierGraph.json"}}}}}