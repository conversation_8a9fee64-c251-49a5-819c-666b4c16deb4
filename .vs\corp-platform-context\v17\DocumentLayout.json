{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\corp-platform-context\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\source\\repos\\corp-platform-context\\.gitlab\\ci_cd.yml||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:.gitlab\\ci_cd.yml||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\source\\repos\\corp-platform-context\\Dockerfile||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:Dockerfile||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\source\\repos\\corp-platform-context\\sonar-project.properties||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:sonar-project.properties||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\source\\repos\\corp-platform-context\\README.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:README.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\source\\repos\\corp-platform-context\\src\\.gitignore||{3B902123-F8A7-4915-9F01-361F908088D0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\.gitignore||{3B902123-F8A7-4915-9F01-361F908088D0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\source\\repos\\corp-platform-context\\src\\.gitkeep||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\.gitkeep||{8B382828-6202-11D1-8870-0000F87579D2}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 7, "Children": [{"$type": "Bookmark", "Name": "ST:128:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:129:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Document", "DocumentIndex": 2, "Title": "sonar-project.properties", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\corp-platform-context\\sonar-project.properties", "RelativeDocumentMoniker": "sonar-project.properties", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\corp-platform-context\\sonar-project.properties", "RelativeToolTip": "sonar-project.properties", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001001|", "WhenOpened": "2025-09-22T19:21:29.813Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "README.md", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\corp-platform-context\\README.md", "RelativeDocumentMoniker": "README.md", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\corp-platform-context\\README.md", "RelativeToolTip": "README.md", "ViewState": "AgIAAAAAAAAAAAAAAAAAABMAAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-09-22T15:06:01.884Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": ".gitkeep", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\corp-platform-context\\src\\.gitkeep", "RelativeDocumentMoniker": "src\\.gitkeep", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\corp-platform-context\\src\\.gitkeep", "RelativeToolTip": "src\\.gitkeep", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001001|", "WhenOpened": "2025-09-22T14:44:00.109Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": ".giti<PERSON>re", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\corp-platform-context\\src\\.gitignore", "RelativeDocumentMoniker": "src\\.gitignore", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\corp-platform-context\\src\\.gitignore", "RelativeToolTip": "src\\.gitignore", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001001|", "WhenOpened": "2025-09-22T14:43:58.836Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "Dockerfile", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\corp-platform-context\\Dockerfile", "RelativeDocumentMoniker": "Dockerfile", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\corp-platform-context\\Dockerfile", "RelativeToolTip": "Dockerfile", "ViewState": "AgIAAAAAAAAAAAAAAAAAABoAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001001|", "WhenOpened": "2025-09-22T14:22:40.442Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "ci_cd.yml", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\corp-platform-context\\.gitlab\\ci_cd.yml", "RelativeDocumentMoniker": ".gitlab\\ci_cd.yml", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\corp-platform-context\\.gitlab\\ci_cd.yml*", "RelativeToolTip": ".gitlab\\ci_cd.yml*", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003775|", "WhenOpened": "2025-09-22T14:06:08.728Z", "EditorCaption": ""}]}]}]}