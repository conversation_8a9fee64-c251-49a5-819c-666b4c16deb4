using PlatformContext.Modules.Headers.Dtos;

namespace PlatformContext.Modules.Headers
{
    public class DeleteHeaderUseCase
    {
        private readonly IHeadersRepository _headersRepository;

        public DeleteHeaderUseCase(IHeadersRepository headersRepository)
        {
            _headersRepository = headersRepository;
        }

        public async Task ExecuteAsync(string id)
        {
            if (string.IsNullOrWhiteSpace(id))
                throw new ArgumentException("Id cannot be null or empty.", nameof(id));

            await _headersRepository.DeleteAsync(id);
        }
    }
}