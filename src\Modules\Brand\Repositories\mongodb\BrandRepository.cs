using MongoDB.Driver;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PlatformContext.Modules.Brand
{
    public class BrandRepository : IBrandRepository
    {
        private readonly IMongoCollection<Brand> _brandsCollection;

        public BrandRepository(IMongoDatabase database)
        {
            _brandsCollection = database.GetCollection<Brand>("Brands");
        }

        public async Task<(List<Brand> Brands, long TotalCount)> GetAllAsync(int pageNumber, int pageSize)
        {
            var skip = (pageNumber - 1) * pageSize;
            var filter = Builders<Brand>.Filter.Empty;

            var brands = await _brandsCollection
                .Find(filter)
                .Skip(skip)
                .Limit(pageSize)
                .ToListAsync();

            var totalCount = await _brandsCollection.CountDocumentsAsync(filter);

            return (brands, totalCount);
        }

        public async Task<Brand> GetByIdAsync(string id)
        {
            return await _brandsCollection.Find(brand => brand.Id == id).FirstOrDefaultAsync();
        }

        public async Task CreateAsync(Brand brand)
        {
            await _brandsCollection.InsertOneAsync(brand);
        }

        public async Task UpdateAsync(string id, Brand updatedBrand)
        {
            if (string.IsNullOrWhiteSpace(id))
            {
                throw new ArgumentException("ID cannot be null or empty", nameof(id));
            }

            if (updatedBrand == null)
            {
                throw new ArgumentNullException(nameof(updatedBrand), "Updated brand cannot be null.");
            }

            var filter = Builders<Brand>.Filter.Eq(brand => brand.Id, id);
            var result = await _brandsCollection.ReplaceOneAsync(filter, updatedBrand);

            if (result.MatchedCount == 0)
            {
                throw new KeyNotFoundException($"Brand with ID {id} not found.");
            }
        }



        public async Task DeleteAsync(string id)
        {
            await _brandsCollection.DeleteOneAsync(brand => brand.Id == id);
        }
    }
}