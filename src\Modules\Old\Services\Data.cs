﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using PlatformContext.Services.Model;

namespace PlatformContext.Services
{
    public static class Data
    {
        public static IReadOnlyList<BrandContext> Brands { get; private set; }
        public static Navigation Navigation { get; private set; }
        public static Headers Headers { get; private set; }

        static Data()
        {
            BuildBrandContexts();
            LoadHeadersData();
        }

        private static void BuildBrandContexts()
        {
            string basePath = AppContext.BaseDirectory;

            Brands = new List<BrandContext>
            {
                CreateContext(File.ReadAllText(Path.Combine(basePath, "Modules", "Old", "AppData", "CVCSITE.json"), Encoding.Latin1)),
                CreateContext(File.ReadAllText(Path.Combine(basePath, "Modules", "Old", "AppData", "IUPP.json"), Encoding.Latin1)),
                CreateContext(File.ReadAllText(Path.Combine(basePath, "Modules", "Old", "AppData", "Sub.json"), Encoding.Latin1)),
                CreateContext(File.ReadAllText(Path.Combine(basePath, "Modules", "Old", "AppData", "SubMobile.json"), Encoding.Latin1)),
                CreateContext(File.ReadAllText(Path.Combine(basePath, "Modules", "Old", "AppData", "CVCCorp.json"), Encoding.Latin1))
            };
        }

        public static void LoadNavigationData(string brand)
        {
            string basePath = AppContext.BaseDirectory;
            string fileName = brand.Equals("Submarino", StringComparison.OrdinalIgnoreCase) ? "NavigationSub.json" : "Navigation.json";
            
            string jsonPath = Path.Combine(basePath, "Modules", "Old", "AppData", fileName);
            if (!File.Exists(jsonPath))
            {
                throw new FileNotFoundException($"Arquivo de navegação não encontrado: {fileName}");
            }
            
            string json = File.ReadAllText(jsonPath, Encoding.UTF8);
            Navigation = JsonSerializer.Deserialize<NavigationWrapper>(json, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase })?.Navigation;
        }

        private static void LoadHeadersData()
        {
            string jsonPath = Path.Combine(Directory.GetCurrentDirectory(), "Modules", "Old", "AppData", "Headers.json");
            if (!File.Exists(jsonPath))
            {
                throw new FileNotFoundException("Arquivo de headers não encontrado.");
            }
            
            string json = File.ReadAllText(jsonPath, Encoding.UTF8);
            Headers = JsonSerializer.Deserialize<HeadersWrapper>(json, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase })?.Headers;
        }

        public class HeadersWrapper
        {
            public Headers Headers { get; set; }
        }

        public class NavigationWrapper
        {
            public Navigation Navigation { get; set; }
        }

        private static BrandContext CreateContext(string serialized) =>
            JsonSerializer.Deserialize<BrandContext>(serialized, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase });
    }
}