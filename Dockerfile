# Use a versão mais recente do SDK do .NET
FROM mcr.microsoft.com/dotnet/sdk:6.0 AS build-env
WORKDIR /app

# Copiar csproj e restaurar dependências
COPY src/PlatformContext.csproj ./
RUN dotnet restore

# Adicionar pacotes Instana
RUN dotnet add PlatformContext.csproj package Instana.Tracing.Core
RUN dotnet add PlatformContext.csproj package Instana.Tracing.Core.Rewriter.Linux

# Copiar código fonte
COPY src/PlatformContext.csproj ./

# Build da aplicação
RUN dotnet publish -c Release -o out

# Build da imagem
FROM mcr.microsoft.com/dotnet/aspnet:6.0
WORKDIR /app
COPY --from=build-env /app/out .

ENV DOTNET_STARTUP_HOOKS=/app/Instana.Tracing.Core.dll
ENV CORECLR_ENABLE_PROFILING=1
ENV CORECLR_PROFILER={cf0d821e-299b-5307-a3d8-b283c03916dd}
ENV CORECLR_PROFILER_PATH=/app/instana_tracing/CoreProfiler.so

ENTRYPOINT ["dotnet", "corp-platform-context.dll"]
