FROM mcr.microsoft.com/dotnet/sdk:6.0 AS build
WORKDIR /workspace

# Copia TODO o contexto (raiz do zip que o pipeline descompactou)
COPY . .

# Caminho padrão do seu projeto. Se não existir, faremos autodetecção.
ARG PROJECT_PATH=src/PlatformContext.csproj

# Restaura e publica (com autodetecção do .csproj quando necessário)
RUN set -e; \
    if [ ! -f "$PROJECT_PATH" ]; then \
        echo "Projeto padrão não encontrado em '$PROJECT_PATH'. Buscando *.csproj..."; \
        FOUND="$(find . -maxdepth 3 -name '*.csproj' | head -n1)"; \
        if [ -z "$FOUND" ]; then echo 'Nenhum .csproj encontrado no contexto.'; exit 1; fi; \
        PROJECT_PATH="$FOUND"; \
    fi; \
    echo "Usando projeto: $PROJECT_PATH"; \
    dotnet restore "$PROJECT_PATH"; \
    \
     dotnet add "$PROJECT_PATH" package Instana.Tracing.Core; \
     dotnet add "$PROJECT_PATH" package Instana.Tracing.Core.Rewriter.Linux; \
    \
    dotnet publish "$PROJECT_PATH" -c Release -o /app/out

# ===== runtime =====
FROM mcr.microsoft.com/dotnet/aspnet:6.0
WORKDIR /app

# Copia a saída publicada
COPY --from=build /app/out ./

# --- Instana: garanta que esses artefatos existam na pasta /app ---
ENV DOTNET_STARTUP_HOOKS=/app/Instana.Tracing.Core.dll
ENV CORECLR_ENABLE_PROFILING=1
ENV CORECLR_PROFILER={cf0d821e-299b-5307-a3d8-b283c03916dd}
ENV CORECLR_PROFILER_PATH=/app/instana_tracing/CoreProfiler.so
# Se o pacote/artefatos não estiverem no publish, copie-os manualmente no build.

# Ajuste o nome do DLL abaixo se o assembly não se chamar PlatformContext.dll
ENTRYPOINT ["dotnet", "PlatformContext.dll"]
