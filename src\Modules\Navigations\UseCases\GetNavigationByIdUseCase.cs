using System.Threading.Tasks;

namespace PlatformContext.Modules.Navigation
{
    public class GetNavigationByIdUseCase
    {
        private readonly INavigationRepository _navigationRepository;

        public GetNavigationByIdUseCase(INavigationRepository navigationRepository)
        {
            _navigationRepository = navigationRepository;
        }

        public async Task<Navigation> ExecuteAsync(string id)
        {
            var navigation = await _navigationRepository.GetByIdAsync(id);
            if (navigation == null)
            {
                throw new KeyNotFoundException($"Navegação com ID {id} não encontrada.");
            }
            return navigation;
        }
    }
}