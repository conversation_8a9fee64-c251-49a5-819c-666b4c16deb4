using System.Collections.Generic;
using System.Threading.Tasks;

namespace PlatformContext.Modules.Brand
{
    public class ListAllBrandsUseCase
    {
        private readonly IBrandRepository _brandRepository;

        public ListAllBrandsUseCase(IBrandRepository brandRepository)
        {
            _brandRepository = brandRepository;
        }

        public async Task<(List<Brand> Brands, long TotalCount)> ExecuteAsync(int pageNumber, int pageSize)
        {
            return await _brandRepository.GetAllAsync(pageNumber, pageSize);
        }
    }
}