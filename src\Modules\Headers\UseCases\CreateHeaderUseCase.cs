using PlatformContext.Modules.Headers.Dtos;

namespace PlatformContext.Modules.Headers
{
    public class CreateHeaderUseCase
    {
        private readonly IHeadersRepository _headersRepository;

        public CreateHeaderUseCase(IHeadersRepository headersRepository)
        {
            _headersRepository = headersRepository;
        }

        public async Task<string> ExecuteAsync(HeadersDto headersDto)
        {
            if (headersDto == null || headersDto.Items == null || headersDto.Items.Count == 0)
                throw new ArgumentException("HeadersDto or Items cannot be null or empty.", nameof(headersDto));

            var headerInput = new Header
            {
                Id = headersDto.Id,
                Name = headersDto.Name,
                Headers = new HeaderItems
                {
                    Items = headersDto.Items.ConvertAll(itemDto => new Item
                    {
                        Id = itemDto.Id,
                        HeaderTitleMessages = itemDto.HeaderTitleMessages
                    })
                }
            };

            await _headersRepository.CreateAsync(headerInput);

            return headerInput.Id;
        }
    }
}
