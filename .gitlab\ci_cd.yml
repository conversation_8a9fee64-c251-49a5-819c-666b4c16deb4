include:
  - remote: 'https://git.cvc.com.br/devops/gitlab/ci-cd-template-files/raw/release/2.0.0/default_jobs.yml'

variables:
  BUILD_IMAGE: mcr.microsoft.com/dotnet/sdk:6.0
  BUILD_CMD: |
    cd src
    dotnet restore PlatformContext.csproj
    dotnet add PlatformContext.csproj package Instana.Tracing.Core
    dotnet add PlatformContext.csproj package Instana.Tracing.Core.Rewriter.Linux
    dotnet publish PlatformContext.csproj -c Release -o ../out
  DOCKERFILE_PATH: Dockerfile
  TECHNOLOGY: dotnet
  # Configurações do SonarQube
  SONAR_PROJECT_KEY: corp-platform-context
  SONAR_PROJECT_NAME: corp-platform-context
  SONAR_SOURCES: src
  SONAR_EXCLUSIONS: "**/bin/**,**/obj/**,**/out/**,**/publish/**"
  # Configuração da solução - caminho correto
  SONAR_DOTNET_SOLUTION: "src/PlatformContext.sln"

stages:
  - local-merge
  - build
  - scan
  - configure-infra
  - push-argocd-repo


