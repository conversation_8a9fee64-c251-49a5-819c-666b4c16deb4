include:
  - remote: 'https://git.cvc.com.br/devops/gitlab/ci-cd-template-files/raw/release/2.0.0/default_jobs.yml'

variables:
  BUILD_IMAGE: mcr.microsoft.com/dotnet/sdk:6.0
  BUILD_CMD: |
    dotnet restore
    dotnet add PlatformContext.csproj package Instana.Tracing.Core
    dotnet add PlatformContext.csproj package Instana.Tracing.Core.Rewriter.Linux
    dotnet publish -c Release -o out
  DOCKERFILE_PATH: Dockerfile
  TECHNOLOGY: dotnet
  # Configurações do SonarQube
  SONAR_PROJECT_KEY: corp-platform-context
  SONAR_PROJECT_NAME: corp-platform-context
  SONAR_SOURCES: src
  SONAR_EXCLUSIONS: "**/bin/**,**/obj/**,**/out/**,**/publish/**"
  SONAR_DOTNET_SOLUTION: "src/PlatformContext.sln"
  # Comando customizado para preparar o SonarQube
  SONAR_PRE_BUILD_CMD: |
    echo "Preparando ambiente para SonarQube..."
    ls -la
    ls -la src/
    echo "Arquivo de solução encontrado em: src/PlatformContext.sln"
    dotnet build src/PlatformContext.sln

stages:
  - local-merge
  - build
  - scan
  - configure-infra
  - push-argocd-repo

# Sobrescrever o job de scan do SonarQube
sonarqube-check:
  stage: scan
  image: gru.ocir.io/grprod/devops/sonar-scanner:1.0.1
  before_script:
    - echo "Preparando ambiente para SonarQube..."
    - ls -la
    - ls -la src/
    - echo "Verificando arquivo de solução..."
    - test -f src/PlatformContext.sln && echo "Arquivo encontrado!" || echo "Arquivo não encontrado!"
  script:
    - cd src
    - dotnet build PlatformContext.sln
    - cd ..
    - sonar-scanner
      -Dsonar.projectKey=corp-platform-context
      -Dsonar.projectName=corp-platform-context
      -Dsonar.sources=src
      -Dsonar.exclusions="**/bin/**,**/obj/**,**/out/**,**/publish/**"
      -Dsonar.dotnet.solution=src/PlatformContext.sln
      -Dsonar.host.url=$SONAR_HOST_URL
      -Dsonar.login=$SONAR_TOKEN
  only:
    - master
    - develop
    - /^feat\/.*$/


