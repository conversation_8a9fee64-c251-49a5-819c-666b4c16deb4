include:
  - remote: 'https://git.cvc.com.br/devops/gitlab/ci-cd-template-files/raw/release/2.0.0/default_jobs.yml'

variables:
  BUILD_IMAGE: mcr.microsoft.com/dotnet/sdk:6.0
  BUILD_CMD: |
    cd src
    dotnet restore PlatformContext.csproj
    dotnet add PlatformContext.csproj package Instana.Tracing.Core
    dotnet add PlatformContext.csproj package Instana.Tracing.Core.Rewriter.Linux
    dotnet publish PlatformContext.csproj -c Release -o ../out
  DOCKERFILE_PATH: Dockerfile
  TECHNOLOGY: dotnet
  # Sobrescrever configurações do SonarQube
  SONAR_PROJECT_KEY: corp-platform-context
  SONAR_PROJECT_NAME: corp-platform-context
  SONAR_SOURCES: src
  SONAR_EXCLUSIONS: "**/bin/**,**/obj/**,**/out/**,**/publish/**"
  # Desabilitar MSBuild e usar análise de código fonte
  SONAR_SCANNER_OPTS: "-Dsonar.cs.analyzer.projectOutPaths= -Dsonar.dotnet.excludeGeneratedCode=true"
  # Forçar modo de análise sem build
  SONAR_DOTNET_SOLUTION: ""
  SONAR_MSBUILD_SOLUTION: ""

stages:
  - local-merge
  - build
  - scan
  - configure-infra
  - push-argocd-repo


