include:
  - remote: 'https://git.cvc.com.br/devops/gitlab/ci-cd-template-files/raw/release/2.0.0/default_jobs.yml'

variables:
  BUILD_IMAGE: mcr.microsoft.com/dotnet/sdk:6.0
  BUILD_CMD: |
    cd src
    dotnet restore PlatformContext.csproj
    dotnet add PlatformContext.csproj package Instana.Tracing.Core
    dotnet add PlatformContext.csproj package Instana.Tracing.Core.Rewriter.Linux
    dotnet publish PlatformContext.csproj -c Release -o ../out
  DOCKERFILE_PATH: Dockerfile
  TECHNOLOGY: dotnet
  SONAR_VSTEST_REPORTS_PATHS: "**/__no_tests__/*.trx"
  SONAR_OPENCOVER_REPORTS_PATHS: "**/__no_tests__/coverage.opencover.xml"

stages:
  - local-merge
  - build
  - scan
  - configure-infra
  - push-argocd-repo


