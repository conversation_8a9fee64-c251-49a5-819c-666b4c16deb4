using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using PlatformContext.Services;
using PlatformContext.Services.Model;
using Microsoft.AspNetCore.Cors;

namespace PlatformContext
{
    [ApiController, Route("api/[controller]")]
    public class ContextController : ControllerBase
    {
        private readonly ILogger<ContextController> _logger;
        public ContextController(ILogger<ContextController> logger)
        {
            _logger = logger;
        }

        [HttpGet]
        public ActionResult<IEnumerable<BrandContext>> Get()
        {
            return Ok(Data.Brands);
        }
    }
}
