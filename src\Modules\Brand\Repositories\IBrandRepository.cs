using System.Collections.Generic;
using System.Threading.Tasks;

namespace PlatformContext.Modules.Brand
{
    public interface IBrandRepository
    {
        Task<(List<Brand> Brands, long TotalCount)> GetAllAsync(int pageNumber, int pageSize);
        Task<Brand> GetByIdAsync(string id);
        Task CreateAsync(Brand brand);
        Task UpdateAsync(string id, Brand updatedBrand);
        Task DeleteAsync(string id);
    }
}