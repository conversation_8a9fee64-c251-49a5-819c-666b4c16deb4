namespace PlatformContext.Modules.Brand.Dtos
{
    public class BrandDto
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string BrandType { get; set; } = string.Empty;
        public ThemeDto Theme { get; set; } = new ThemeDto();
        public LogoDto Logo { get; set; } = new LogoDto();
        public string[] Hosts { get; set; } = Array.Empty<string>();
        public FooterDto Footer { get; set; } = new FooterDto();
        public GtwDataDto GtwData { get; set; } = new GtwDataDto();
        public string GTM { get; set; } = string.Empty;
        public string OmnilogicScript { get; set; } = string.Empty;
        public string ImageProcessorPath { get; set; } = string.Empty;
        public HeaderImageBgDto HeaderImageBg { get; set; } = new HeaderImageBgDto();
        public CustomImageBgProductDto CustomImageBgProduct { get; set; } = new CustomImageBgProductDto();
        public string HomeMotorImageBg { get; set; } = string.Empty;
        public FavIconDto Favicon { get; set; } = new FavIconDto();
        public Dictionary<string, string> HomeMessages { get; set; }
        public HotjarIdsDto HotjarIds { get; set; } = new HotjarIdsDto();
        public string TermConditionLink { get; set; } = string.Empty;
        public string PrivacyLink { get; set; } = string.Empty;
        public string[] FeatureToggles { get; set; } = Array.Empty<string>();
        public string PendingPaymentContact { get; set; } = string.Empty;
        public List<ProductRedirectInfoDto> ProductsRedirect { get; set; } = new();
        public string[] RedirectsToLogin { get; set; } = Array.Empty<string>();
    }

    public class ThemeDto
    {
        public string ThemeName { get; set; } = string.Empty;
        public string Primary { get; set; } = string.Empty;
        public string Secondary { get; set; } = string.Empty;
    }

    public class LogoDto
    {
        public string Dark { get; set; } = string.Empty;
        public string Light { get; set; } = string.Empty;
        public string PreferredWidth { get; set; } = string.Empty;
        public string PreferredWidthMobile { get; set; } = string.Empty;
    }
    
    public class FooterDto
    {
        public List<FooterSectionDto> Sections { get; set; } = new List<FooterSectionDto>();
        public string AppTitle { get; set; } = string.Empty;
        public List<AppDto> Apps { get; set; } = new List<AppDto>();
        public string SocialNetworksTitle { get; set; } = string.Empty;
        public List<SocialNetworkDto> SocialNetworks { get; set; } = new List<SocialNetworkDto>();
        public List<PaymentDto> Payments { get; set; } = new List<PaymentDto>();
        public List<ImportantReminderDto> ImportantReminders { get; set; } = new List<ImportantReminderDto>();
    }

    public class FooterSectionDto
    {
        public string Title { get; set; } = string.Empty;
        public List<FooterItemDto> Items { get; set; } = new List<FooterItemDto>();
    }

    public class FooterItemDto
    {
        public string Type { get; set; } = string.Empty;
        public string Text { get; set; } = string.Empty;
        public Dictionary<string, string> Attributes { get; set; } = new Dictionary<string, string>();
    }

    public class AppDto
    {
        public string Name { get; set; } = string.Empty;
        public string Image { get; set; } = string.Empty;
        public string Url { get; set; } = string.Empty;
    }

    public class SocialNetworkDto
    {
        public string Name { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public Dictionary<string, string> Attributes { get; set; } = new Dictionary<string, string>();
    }

    public class PaymentDto
    {
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public List<PaymentFlagDto> Flags { get; set; } = new List<PaymentFlagDto>();
    }

    public class PaymentFlagDto
    {
        public string Name { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;

        public string Description { get; set; } = string.Empty;
    }

    public class ImportantReminderDto
    {
        public string Label { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
    }

    public class GtwDataDto
    {
        public string BranchId { get; set; } = string.Empty;
        public string AgentSign { get; set; } = string.Empty;
    }

    public class HeaderImageBgDto
    {
        public string Desktop { get; set; } = string.Empty;
        public string Mobile { get; set; } = string.Empty;
    }

    public class CustomImageBgProductDto
    {
        public Dictionary<string, ProductNameDto> ProductNames { get; set; } = new Dictionary<string, ProductNameDto>();
    }

    public class ProductNameDto
    {
        public string Desktop { get; set; } = string.Empty;
        public string Mobile { get; set; } = string.Empty;
    }

    public class FavIconDto
    {
        public string Small { get; set; } = string.Empty;
        public string Big { get; set; } = string.Empty;
    }

    public class HotjarIdsDto
    {
        public string MyTravelsFront { get; set; } = string.Empty;
        public string CustomerLoyalty { get; set; } = string.Empty;
    }

    public class ProductRedirectInfoDto
    {
        public string Name { get; set; } = string.Empty;
        public string Url { get; set; } = string.Empty;
    }

}
