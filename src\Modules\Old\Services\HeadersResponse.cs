using System.Collections.Generic;

namespace PlatformContext.Services.Model
{
    public class HeadersResponse
    {
        public Headers Headers { get; set; } = new Headers();
    }

    public class Headers
    {
        public List<HeaderItem> Items { get; set; } = new List<HeaderItem>();
    }

    public class HeaderItem
    {
        public string Id { get; set; }
        public string Label { get; set; }
        public string Href { get; set; }
        public string Icon { get; set; }
        public List<string> HeaderTitleMessages { get; set; } = new List<string>();
    }
}
