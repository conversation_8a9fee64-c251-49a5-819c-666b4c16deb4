﻿{
  "id": "Submarino",
  "name": "Submarino Viagens",
  "brand": "Sub",
  "hosts": [
    "www.submarinoviagens.com.br",
    "",
    "localhost",
    "sub",
    "submarinoviagens",
    "local.sub",
    "local.submarinoviagens",
    "ti.submarinoviagens.com.br",
    "qa.submarinoviagens.com.br",
    "submarinoviagens.com.br"
  ],

  "theme": {
    "themeName": "sub",
    "air": "#0033FF",
    "alert": "#FF5D00",
    "approved": "#67C453",
    "backdrop": "rgba(0,0,0, .3)",
    "backdrop70": "rgba(0,0,0, .7)",
    "backdrop100": "#000",
    "border": "#DDD",
    "canceled": "#FF3344",
    "car": "#0074C5",
    "contrastSecondaryText": "#333",
    "disabled": "#EFEFEF",
    "error": "#FF3344",
    "hotel": "#97007C",
    "offwhite": "#F2F5F8",
    "packageColor": "#FF5D00",
    "primary": "#237FD0",
    "primaryAlpha": "rgba(35, 127, 208, .3)",
    "primaryAlphaDark": "rgba(255, 255, 255, 0.15)",
    "primaryDark": "#175F9F",
    "primaryDarkHover": "#2374BB",
    "primaryLight": "#3493da",
    "primaryLighter": "rgba(52, 147, 218, 0.05)",
    "primaryText": "#333",
    "processed": "#FF5D00",
    "secondary": "#F8DB1C",
    "secondaryDark": "#E3C91E",
    "secondaryHover": "#FAE661",
    "secondaryLighter": "rgba(250, 230, 97, 0.1)",
    "secondaryText": "#999",
    "spot": "#FF00CC",
    "success": "#67C453",
    "successHover": "#4c9c3a",
    "tabs": "#2374BB",
    "tabsHover": "#237FD0",
    "white": "#FFF",
    "white30": "rgba(255,255,255, .3)",
    "regular": "HindMRegular",
    "bold": "HindMBold"
  },
  "menu": {
    "others": [
      {
        "label": "Destinos",
        "link": "/destinos",
        "target": "_blank",
        "icon": "Destinations"
      },
      {
        "label": "Meu Câmbio",
        "link": "https://submarinoviagens.meucambio.com.br/#/",
        "target": "_blank",
        "icon": "MoneyExchange"
      }
    ],
    "hiddenPrincipals": [],
    "principals": [
      {
        "product": "air",
        "label": "Passagens",
        "link": "/passagens-aereas",
        "icon": "Air",
        "id": "menu-passagens"
      },
      {
        "product": "hotel",
        "label": "Hotéis",
        "link": "/hotel",
        "icon": "Hotel",
        "id": "menu-hoteis"
      },
      {
        "product": "pacotes",
        "label": "Pacotes",
        "link": "/pacotes-turisticos",
        "icon": "PackageIcon",
        "id": "menu-pacotes"
      },
      {
        "label": "Resorts",
        "link": "/resorts",
        "icon": "Resort",
        "id": "menu-resorts"
      }
    ],
    "phone": " (11) 3003 2989",
    "phoneSales": " 0800-049-5275",
    "help": "/atendimento/faq",
    "register": "/minhasviagens/cadastro",
    "login": "/minhasviagens/"
  },
  "footer": {
    "sections": [
      {
        "title": "Submarino Viagens",
        "items": [
          {
            "type": "GTMLINK",
            "text": "Sobre nós",
            "attributes": {
              "data-gtm-event-category": "subviagens:geral",
              "data-gtm-event-action": "footer:clique",
              "data-gtm-event-label": "Sobre o Submarino Viagens",
              "target": "_blank",
              "href": "https://www.submarinoviagens.com.br/institucional",
              "title": "Sobre o Submarino Viagens",
              "rel": "noopener noreferrer"
            }
          },
          {
            "type": "GTMLINK",
            "text": "Imprensa",
            "attributes": {
              "data-gtm-event-category": "subviagens:geral",
              "data-gtm-event-action": "footer:clique",
              "data-gtm-event-label": "Imprensa",
              "target": "_blank",
              "href": "https://www.submarinoviagens.com.br/imprensa",
              "title": "Imprensa",
              "rel": "noopener noreferrer"
            }
          },

          {
            "type": "GTMLINK",
            "text": "Instituto CVC",
            "attributes": {
              "data-gtm-event-category": "subviagens:geral",
              "data-gtm-event-action": "footer:clique",
              "data-gtm-event-label": "Instituto CVC",
              "target": "_blank",
              "href": "https://www.institutocvc.com.br/",
              "title": "Instituto CVC",
              "rel": "noopener noreferrer"
            }
          },
          {
            "type": "GTMLINK",
            "text": "REprograma CVC",
            "attributes": {
              "data-gtm-event-category": "cvc:geral",
              "data-gtm-event-action": "footer:clique",
              "data-gtm-event-label": "REprograma CVC",
              "target": "_blank",
              "href": "https://www.institutocvc.com.br/reprograma/?utm_source=submarinoviagens&utm_medium=site&utm_campaign=mkt",
              "title": "REprograma CVC",
              "rel": "noopener noreferrer"
            }
          }
        ]
      },
      {
        "title": "Central de atendimento",
        "items": [
          {
            "type": "PHONELINK",
            "text": " 11 3003 2989",
            "attributes": {
              "href": "tel: (11)3003-2989"
            }
          },
          {
            "type": "TEXT",
            "text": "Segunda à Sábado e feriados: 09h às 21h"
          },
          {
            "type": "GTMLINK",
            "text": "Precisa de Ajuda?",
            "attributes": {
              "data-gtm-event-category": "subviagens:geral",
              "data-gtm-event-action": "footer:clique",
              "data-gtm-event-label": "Atendimento ao cliente",
              "target": "_self",
              "href": "/atendimento/faq",
              "title": "Atendimento ao cliente",
              "rel": "noopener noreferrer"
            }
          }
        ]
      },
      {
        "title": "Termos",
        "items": [
          {
            "type": "GTMLINK",
            "text": "Condições Gerais",
            "attributes": {
              "data-gtm-event-category": "subviagens:geral",
              "data-gtm-event-action": "footer:clique",
              "data-gtm-event-label": "Condições Gerais",
              "target": "_blank",
              "href": "https://www.submarinoviagens.com.br/regras-e-condicoes/condicoes-gerais.aspx",
              "title": "Condições Gerais",
              "rel": "noopener noreferrer"
            }
          },
          {
            "type": "GTMLINK",
            "text": "Politica de privacidade",
            "attributes": {
              "data-gtm-event-category": "subviagens:geral",
              "data-gtm-event-action": "footer:clique",
              "data-gtm-event-label": "Política de Privacidade",
              "target": "_blank",
              "href": "https://www.submarinoviagens.com.br/institucional/politicas-de-privacidade",
              "title": "Política de Privacidade",
              "rel": "noopener noreferrer"
            }
          },
          {
            "type": "GTMLINK",
            "text": "Formas de pagamento",
            "attributes": {
              "data-gtm-event-category": "subviagens:geral",
              "data-gtm-event-action": "footer:clique",
              "data-gtm-event-label": "Formas de Pagamento",
              "target": "_blank",
              "href": "https://www.submarinoviagens.com.br/institucional/formas-de-pagamento",
              "title": "Formas de Pagamento",
              "rel": "noopener noreferrer"
            }
          },
          {
            "type": "GTMLINK",
            "text": "Compra Segura",
            "attributes": {
              "data-gtm-event-category": "subviagens:geral",
              "data-gtm-event-action": "footer:clique",
              "data-gtm-event-label": "Compra Segura",
              "target": "_blank",
              "href": "https://www.submarinoviagens.com.br/institucional/seguranca",
              "title": "Compra Segura",
              "rel": "noopener noreferrer"
            }
          },
          {
            "type": "GTMLINK",
            "text": "Código de Conduta Ética",
            "attributes": {
              "data-gtm-event-category": "cvc:geral",
              "data-gtm-event-action": "footer:clique",
              "data-gtm-event-label": "Código de Conduta Ética",
              "target": "_blank",
              "href": "https://www.cvccorp.com.br/contato-e-imprensa/contato/canal-de-etica-e-codigo-de-conduta-etica/",
              "title": "Código de Conduta Ética",
              "rel": "noopener noreferrer"
            }
          }
        ]
      }
    ],
    "appTitle": "Baixe nosso aplicativo",
    "apps": [
      {
        "name": "App Store",
        "image": "https://www.submarinoviagens.com.br/imageproc/image/upload/v1/global/icons/social/socialAppstore.svg",
        "url": "https://apps.apple.com/us/app/submarino-viagens/id1438773877"
      },
      {
        "name": "Google Play",
        "image": "https://www.submarinoviagens.com.br/imageproc/image/upload/v1/global/icons/social/socialGoogleplay.svg",
        "url": "https://play.google.com/store/apps/details?id=com.cvc.subviagens"
      }
    ],
    "socialNetworksTitle": "",
    "socialNetworks": [],
    "payments": [
      {
        "title": "Formas de pagamento",
        "flags": [
          {
            "name": "Visa",
            "icon": "https://www.submarinoviagens.com.br/imageproc/image/upload/v1/global/logos/cards/visa.svg"
          },
          {
            "name": "Mastercard",
            "icon": "https://www.submarinoviagens.com.br/imageproc/image/upload/v1/global/logos/payments/mastercard.svg"
          },
          {
            "name": "Elo",
            "icon": "https://www.submarinoviagens.com.br/imageproc/image/upload/v1/global/logos/cards/elo.svg"
          },
          {
            "name": "AmericanExpress",
            "icon": "https://www.submarinoviagens.com.br/imageproc/image/upload/v1/global/logos/cards/american-express.svg"
          },
          {
            "name": "Diners",
            "icon": "https://www.submarinoviagens.com.br/imageproc/image/upload/v1/global/logos/cards/diners.svg"
          },
          {
            "name": "Hipercard",
            "icon": "https://www.submarinoviagens.com.br/imageproc/image/upload/v1/global/logos/cards/hipercard.svg"
          }
        ]
      }
    ],
    "paymentsV2": [
         {
            "title": "Formas de pagamentos",
            "options": [
               {
                  "title": "Cartão de crédito",
                  "description": "",
                  "flags": [
                     {
                        "name": "Visa",
                        "icon": "https://almundo-com-res.cloudinary.com/raw/upload/v1730741007/chui-icons/flags/creditCard/ghost/GhostVisa.svg"
                     },
                     {
                        "name": "Mastercard",
                        "icon": "https://almundo-com-res.cloudinary.com/raw/upload/v1730741017/chui-icons/flags/creditCard/ghost/GhostMastercard.svg"
                     },
                     {
                        "name": "Elo",
                        "icon": "https://almundo-com-res.cloudinary.com/raw/upload/v1730741016/chui-icons/flags/creditCard/ghost/GhostElo.svg"
                     },
                     {
                        "name": "AmericanExpress",
                        "icon": "https://almundo-com-res.cloudinary.com/raw/upload/v1730741006/chui-icons/flags/creditCard/ghost/GhostAmex.svg"
                     },
                     {
                        "name": "Diners",
                        "icon": "https://almundo-com-res.cloudinary.com/raw/upload/v1730741006/chui-icons/flags/creditCard/ghost/GhostDiners.svg"
                     },
                     {
                        "name": "Hipercard",
                        "icon": "https://almundo-com-res.cloudinary.com/raw/upload/v1730741017/chui-icons/flags/creditCard/ghost/GhostHipercard.svg"
                     }
                  ]
               },
               {
                  "title": "Outras formas",
                  "flags": [
                     {
                        "name": "Pix",
                        "icon": "https://www.cvc.com.br/imageproc/image/upload/v1/global/logos/cards/pix.svg"
                     },
                     {
                        "name": "Nubank",
                        "icon": "https://www.cvc.com.br/imageproc/image/upload/v1/global/logos/cards/nuPay.svg",
                        "description": "até 24x para clientes Nubank"
                     }
                  ]
               }
            ]
         }
    ],
    "texts": [
      {
        "text": "Submarino Viagens Ltda - CNPJ:",
        "textOnBreakMobile": "06.179.342/0001-05"
      },
      {
        "text": "Rua da Catequese, 227, 11 andar, sala 111 | ",
        "textOnBreakMobile": "Bairro Jardim, Santo André - SP"
      },
      {
        "text": "CEP: 09090-401",
        "textOnBreakMobile": ""
      }
    ]
  },
  "logo": {
    "dark": "https://www.submarinoviagens.com.br/imageproc/image/upload/f_auto,q_auto:eco,w_50/v1/CVC/platform/logo/sub/logo-escuro.png",
    "light": "https://www.submarinoviagens.com.br/imageproc/image/upload/f_auto,q_auto:eco,w_50/v1/CVC/platform/logo/sub/logo.png",
    "preferredWidth": "50px",
    "preferredWidthMobile": "43px"
  },
  "gtwData": {
    "branchId": "1020",
    "agentSign": "SUB"
  },
  "gtm": "GTM-NR39N2",
  "omnilogicScript": "suba",
  "imageProcessorPath": "https://www.submarinoviagens.com.br/imageproc/",
  "headerImageBg": null,
  "customImageBgProduct": {
    "productNames": {
      "disney": {
        "desktop": "https://ti.submarinoviagens.com.br/platform/assets/tickets-disney/_next/static/images/img_home_banner-78469bd7833ebf77c208e1b56cb0bb23.png",
        "mobile": "https://ti.submarinoviagens.com.br/platform/assets/tickets-disney/_next/static/images/bg_disney_mobile-2e70afb7d44e43acdc47508ae0bba57c.png"
      }
    }
  },
  "homeMotorImageBg": "https://www.submarinoviagens.com.br/imageproc/image/upload/f_auto,q_auto:eco/v1/CVC/platform/bg-motor.svg",
  "favicon": {
    "small": "https://www.submarinoviagens.com.br/imageproc/image/upload/f_auto,q_auto:eco/v1/CVC/platform/favicon/sub/favicon-16x16.png",
    "big": "https://www.submarinoviagens.com.br/imageproc/image/upload/f_auto,q_auto:eco/v1/CVC/platform/favicon/sub/favicon-32x32.png"
  },
  "homeMessages": {
    "index": "Olá, encontre aqui os melhores * voos * para sua viagem!",
    "pacotes-turisticos": "Olá, confira os melhores * pacotes * e economize!",
    "pacotes": "Olá, confira os melhores * pacotes * e economize!",
    "hotel": "Olá, confira os melhores * hotéis * para seu conforto",
    "passagens-aereas": "Olá, encontre aqui os melhores * voos * para sua viagem!",
    "air": "Olá, encontre aqui os melhores * voos * para sua viagem!",
    "tickets": "Olá, veja os melhores * Ingressos * para sua diversão",
    "ingresso": "Olá, veja os melhores * Ingressos * para sua diversão",
    "disney": "Proporcione o mundo mágico da * Disney * para sua família"
  },
  "featureToggles": ["NEW_LOGIN", "PROFILE"],
  "hotjarIds": {
    "myTravelsFront": "763648"
  },
  "termConditionLink": "/regras-e-condicoes/condicoes-gerais.aspx",
  "privacityLink": "/institucional/politicas-de-privacidade",
  "pendingPaymentContact": "(11) 3531-5202",
  "redirectsToLogin": ["mytravels/", "mypoints/"]
}
