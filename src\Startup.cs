﻿using System.Text.Json;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using MongoDB.Driver;
using PlatformContext.Infrastructure.Configurations;
using PlatformContext.Modules.Brand;
using PlatformContext.Modules.Site;
using PlatformContext.Modules.Navigation;
using PlatformContext.Modules.Headers;
using Auth.UseCases;
using Auth.Repositories;
using Auth.Middlewares;
using Microsoft.IdentityModel.Logging;
using System.Text;
using PlatformContext.Middlewares;
namespace PlatformContext
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        public static void Main(string[] args)
        {
            CreateHostBuilder(args).Build().Run();
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.UseStartup<Startup>();
                });

        public void ConfigureServices(IServiceCollection services)
        {
            services.AddCors(options => options.AddPolicy("AllowAnyOrigin", builder =>
                builder.AllowAnyOrigin().AllowAnyHeader().AllowAnyMethod()));

            services.AddControllers()
                .AddJsonOptions(opts =>
                    opts.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase);

            services.AddHealthChecks();

            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo
                {
                    Title = "PlatformContext API",
                    Version = "v1",
                    Description = "API de documentação para PlatformContext"
                });
                c.CustomSchemaIds(type => type.FullName);
            });

            var connectionString = Configuration["MongoDbSettings:ConnectionString"];
            var databaseName = Configuration["MongoDbSettings:DatabaseName"];

            services.Configure<MongoDbSettings>(Configuration.GetSection("MongoDbSettings"));
            services.AddSingleton<IMongoClient>(sp =>
            {
                var settings = sp.GetRequiredService<IOptions<MongoDbSettings>>().Value;
                return new MongoClient(settings.ConnectionString);
            });

            services.AddScoped(sp =>
            {
                var client = sp.GetRequiredService<IMongoClient>();
                var settings = sp.GetRequiredService<IOptions<MongoDbSettings>>().Value;
                return client.GetDatabase(settings.DatabaseName);
            });

            // Registrar Repositórios e Casos de Uso
            services.AddHttpClient<IAuthRepository, AuthRepository>();
            services.AddScoped<LoginUseCase>();
            services.AddScoped<RefreshTokenUseCase>();

            services.AddScoped<IHeadersRepository, HeadersRepository>();
            services.AddScoped<CreateHeaderUseCase>();
            services.AddScoped<GetAllHeadersUseCase>();
            services.AddScoped<GetHeaderByIdUseCase>();
            services.AddScoped<DeleteHeaderUseCase>();
            services.AddScoped<UpdateHeaderUseCase>();

            services.AddScoped<IBrandRepository, BrandRepository>();
            services.AddScoped<CreateBrandUseCase>();
            services.AddScoped<ListAllBrandsUseCase>();
            services.AddScoped<GetBrandByIdUseCase>();
            services.AddScoped<UpdateBrandUseCase>();
            services.AddScoped<DeleteBrandUseCase>();

            services.AddScoped<ISiteRepository, SiteRepository>();
            services.AddScoped<CreateSiteUseCase>();
            services.AddScoped<ListAllSitesUseCase>();
            services.AddScoped<GetSiteByIdUseCase>();
            services.AddScoped<UpdateSiteUseCase>();
            services.AddScoped<DeleteSiteUseCase>();

            services.AddScoped<INavigationRepository, NavigationRepository>();
            services.AddScoped<CreateNavigationUseCase>();
            services.AddScoped<ListAllNavigationsUseCase>();
            services.AddScoped<GetNavigationByIdUseCase>();
            services.AddScoped<UpdateNavigationUseCase>();
            services.AddScoped<DeleteNavigationUseCase>();

            IdentityModelEventSource.ShowPII = true;

            services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
                .AddJwtBearer(options =>
                {
                    options.TokenValidationParameters = new TokenValidationParameters
                    {
                        ValidateIssuer = true,
                        ValidIssuer = "https://orc-springboot-corp-auth.k8s-qa-cvc.com.br",
                        ValidateAudience = true,
                        ValidAudience = "corp-platform-context",
                        ValidateLifetime = true,
                        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes("your-signing-key"))
                    };
                });
            
            services.AddSingleton<LoggingInterceptorMiddleware>();
        }

        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }

            app.UseSwagger();
            app.UseSwaggerUI(c =>
            {
                c.SwaggerEndpoint("/swagger/v1/swagger.json", "PlatformContext API v1");
                c.RoutePrefix = string.Empty;
            });

            app.UseRouting();
            app.UseCors("AllowAnyOrigin");

            app.UseAuthentication(); 
            app.UseAuthorization();

            app.UseMiddleware<AuthenticationMiddleware>(); 

            app.UseHealthChecks("/health", new Microsoft.AspNetCore.Diagnostics.HealthChecks.HealthCheckOptions
            {
                ResponseWriter = async (context, report) =>
                {
                    var result = JsonSerializer.Serialize(new
                    {
                        status = report.Status.ToString(),
                        details = report.Entries
                    });
                    await context.Response.WriteAsync(result);
                }
            });

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });
        }
    }
}
