using System.Threading.Tasks;
using PlatformContext.Modules.Site.Dtos;

namespace PlatformContext.Modules.Site
{
    public class UpdateSiteUseCase
    {
        private readonly ISiteRepository _siteRepository;

        public UpdateSiteUseCase(ISiteRepository siteRepository)
        {
            _siteRepository = siteRepository;
        }

        public async Task<Site> ExecuteAsync(string id, SiteDto siteDto)
        {
            var existingSite = await _siteRepository.GetByIdAsync(id);
            if (existingSite == null)
            {
                throw new KeyNotFoundException($"Site com ID {id} não encontrado para atualização.");
            }

            if (!string.IsNullOrEmpty(siteDto.Name))
                existingSite.Name = siteDto.Name;

            if (!string.IsNullOrEmpty(siteDto.Description))
                existingSite.Description = siteDto.Description;

            if (!string.IsNullOrEmpty(siteDto.BrandId))
                existingSite.BrandId = siteDto.BrandId;

            if (!string.IsNullOrEmpty(siteDto.NavigationId))
                existingSite.NavigationId = siteDto.NavigationId;

            if (!string.IsNullOrEmpty(siteDto.HeaderId))
                existingSite.HeaderId = siteDto.HeaderId;

            await _siteRepository.UpdateAsync(id, existingSite);
            return existingSite;
        }

    }
}