using MongoDB.Bson;
using MongoDB.Driver;
using PlatformContext.Modules.Headers.Dtos;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PlatformContext.Modules.Headers
{
    public class HeadersRepository : IHeadersRepository
    {
        private readonly IMongoCollection<Header> _headersCollection;

        public HeadersRepository(IMongoDatabase database)
        {
            _headersCollection = database.GetCollection<Header>("Headers");
        }

        public async Task<(List<Header> HeaderList, long TotalCount)> GetAllAsync(string? nameHeader, int pageNumber, int pageSize)
        {
            var filter = string.IsNullOrWhiteSpace(nameHeader)
                ? Builders<Header>.Filter.Empty
                : Builders<Header>.Filter.Regex("Name", new BsonRegularExpression(nameHeader, "i"));

            var totalDocuments = await _headersCollection.CountDocumentsAsync(FilterDefinition<Header>.Empty);
            var headers = await _headersCollection
                .Find(FilterDefinition<Header>.Empty)
                .Skip((pageNumber - 1) * pageSize)
                .Limit(pageSize)
                .ToListAsync();

            return (headers, (int)totalDocuments);
        }

        public async Task<Header> GetByIdAsync(string id)
        {
            return await _headersCollection.Find(header => header.Id == id).FirstOrDefaultAsync();
        }

        public async Task CreateAsync(Header headers)
        {
            await _headersCollection.InsertOneAsync(headers);
        }

        public async Task UpdateAsync(string id, Header headers)
        {
            await _headersCollection.ReplaceOneAsync(header => header.Id == id, headers);
        }

        public async Task DeleteAsync(string id)
        {
            await _headersCollection.DeleteOneAsync(header => header.Id == id);
        }
    }
}
