C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\appsettings.Development.json
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\appsettings.json
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\Modules\Old\AppData\CVCCorp.json
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\Modules\Old\AppData\CVCSITE.json
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\Modules\Old\AppData\Headers.json
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\Modules\Old\AppData\IUPP.json
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\Modules\Old\AppData\Navigation.json
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\Modules\Old\AppData\Sub.json
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\Modules\Old\AppData\SubMobile.json
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\Modules\Old\Properties\launchSettings.json
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\DnsClient.dll
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\DotNetEnv.dll
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\Microsoft.AspNetCore.Authentication.JwtBearer.dll
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\Microsoft.Bcl.TimeProvider.dll
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\Microsoft.Extensions.Configuration.Abstractions.dll
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\Microsoft.Extensions.DependencyInjection.dll
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\Microsoft.Extensions.Http.dll
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\Microsoft.Extensions.Logging.dll
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\Microsoft.Extensions.Options.dll
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\Microsoft.IdentityModel.Abstractions.dll
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\Microsoft.IdentityModel.JsonWebTokens.dll
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\Microsoft.IdentityModel.Logging.dll
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\Microsoft.IdentityModel.Protocols.dll
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\Microsoft.IdentityModel.Tokens.dll
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\Microsoft.OpenApi.dll
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\MongoDB.Bson.dll
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\MongoDB.Driver.dll
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\SharpCompress.dll
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\Snappier.dll
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\Sprache.dll
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\Swashbuckle.AspNetCore.Annotations.dll
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\System.Diagnostics.DiagnosticSource.dll
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\System.IdentityModel.Tokens.Jwt.dll
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\ZstdSharp.dll
C:\Users\<USER>\source\repos\corp-platform-context\src\obj\Debug\net6.0\PlatformContext.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\corp-platform-context\src\obj\Debug\net6.0\PlatformContext.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\source\repos\corp-platform-context\src\obj\Debug\net6.0\PlatformContext.AssemblyInfoInputs.cache
C:\Users\<USER>\source\repos\corp-platform-context\src\obj\Debug\net6.0\PlatformContext.AssemblyInfo.cs
C:\Users\<USER>\source\repos\corp-platform-context\src\obj\Debug\net6.0\PlatformContext.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\corp-platform-context\src\obj\Debug\net6.0\staticwebassets.pack.json
C:\Users\<USER>\source\repos\corp-platform-context\src\obj\Debug\net6.0\staticwebassets.build.json
C:\Users\<USER>\source\repos\corp-platform-context\src\obj\Debug\net6.0\staticwebassets.development.json
C:\Users\<USER>\source\repos\corp-platform-context\src\obj\Debug\net6.0\PlatformContext.genruntimeconfig.cache
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\Modules\Old\AppData\NavigationSub.json
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\corp-platform-context.exe
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\corp-platform-context.deps.json
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\corp-platform-context.runtimeconfig.json
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\corp-platform-context.dll
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\corp-platform-context.pdb
C:\Users\<USER>\source\repos\corp-platform-context\src\bin\Debug\net6.0\corp-platform-context.xml
C:\Users\<USER>\source\repos\corp-platform-context\src\obj\Debug\net6.0\corp-platform-context.MvcApplicationPartsAssemblyInfo.cs
C:\Users\<USER>\source\repos\corp-platform-context\src\obj\Debug\net6.0\corp-platform-context.MvcApplicationPartsAssemblyInfo.cache
C:\Users\<USER>\source\repos\corp-platform-context\src\obj\Debug\net6.0\staticwebassets\msbuild.corp-platform-context.Microsoft.AspNetCore.StaticWebAssets.props
C:\Users\<USER>\source\repos\corp-platform-context\src\obj\Debug\net6.0\staticwebassets\msbuild.build.corp-platform-context.props
C:\Users\<USER>\source\repos\corp-platform-context\src\obj\Debug\net6.0\staticwebassets\msbuild.buildMultiTargeting.corp-platform-context.props
C:\Users\<USER>\source\repos\corp-platform-context\src\obj\Debug\net6.0\staticwebassets\msbuild.buildTransitive.corp-platform-context.props
C:\Users\<USER>\source\repos\corp-platform-context\src\obj\Debug\net6.0\scopedcss\bundle\corp-platform-context.styles.css
C:\Users\<USER>\source\repos\corp-platform-context\src\obj\Debug\net6.0\PlatformContext.csproj.CopyComplete
C:\Users\<USER>\source\repos\corp-platform-context\src\obj\Debug\net6.0\corp-platform-context.dll
C:\Users\<USER>\source\repos\corp-platform-context\src\obj\Debug\net6.0\refint\corp-platform-context.dll
C:\Users\<USER>\source\repos\corp-platform-context\src\obj\Debug\net6.0\corp-platform-context.xml
C:\Users\<USER>\source\repos\corp-platform-context\src\obj\Debug\net6.0\corp-platform-context.pdb
C:\Users\<USER>\source\repos\corp-platform-context\src\obj\Debug\net6.0\ref\corp-platform-context.dll
