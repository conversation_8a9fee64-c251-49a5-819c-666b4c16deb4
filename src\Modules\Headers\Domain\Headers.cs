using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System.Collections.Generic;
#nullable disable

namespace PlatformContext.Modules.Headers
{
    public class Header
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string Id { get; set; } = string.Empty;

    public string Name { get; set; } = string.Empty;

    public HeaderItems Headers { get; set; } = new HeaderItems();
}

    public class HeaderItems
    {
        public List<Item> Items { get; set; } = new List<Item>();
    }

    public class Item
    {
        public string Id { get; set; } = string.Empty;
        public List<string> HeaderTitleMessages { get; set; } = new List<string>();
    }

}