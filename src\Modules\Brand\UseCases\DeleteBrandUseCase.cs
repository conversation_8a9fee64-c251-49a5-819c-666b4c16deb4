using System.Threading.Tasks;

namespace PlatformContext.Modules.Brand
{
    public class DeleteBrandUseCase
    {
        private readonly IBrandRepository _brandRepository;

        public DeleteBrandUseCase(IBrandRepository brandRepository)
        {
            _brandRepository = brandRepository;
        }

        public async Task<bool> ExecuteAsync(string id)
        {
            var existingBrand = await _brandRepository.GetByIdAsync(id);
            if (existingBrand == null)
            {
                throw new KeyNotFoundException($"Brand with ID {id} not found.");
            }

            await _brandRepository.DeleteAsync(id);
            return true;
        }
    }
}