using System.Collections.Generic;

namespace PlatformContext.Modules.Navigation.Dtos
{
    public class NavigationDto
    {
        public string Name { get; set; } = string.Empty;
        public List<NavigationItemDto> Items { get; set; } = new List<NavigationItemDto>();
    }

    public class NavigationItemDto
    {
        public string Label { get; set; } = string.Empty;
        public string Url { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public bool IsActive { get; set; } = true;
    }
}