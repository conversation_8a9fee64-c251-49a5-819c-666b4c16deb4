using System.Threading.Tasks;
using PlatformContext.Modules.Brand.Dtos;
using System.Collections.Generic;

namespace PlatformContext.Modules.Brand
{
    public class UpdateBrandUseCase
    {
        private readonly IBrandRepository _brandRepository;

        public UpdateBrandUseCase(IBrandRepository brandRepository)
        {
            _brandRepository = brandRepository;
        }

        public async Task<Brand> ExecuteAsync(string id, BrandDto brandDto)
        {
            if (string.IsNullOrWhiteSpace(id))
            {
                throw new ArgumentException("ID cannot be null or empty", nameof(id));
            }

            if (brandDto == null)
            {
                throw new ArgumentNullException(nameof(brandDto), "Brand DTO cannot be null.");
            }

            // Buscar a marca existente
            var existingBrand = await _brandRepository.GetByIdAsync(id);
            if (existingBrand == null)
            {
                throw new KeyNotFoundException($"Brand with ID {id} not found.");
            }

            // Atualizar os campos
            existingBrand.Name = brandDto.Name ?? existingBrand.Name;
            existingBrand.BrandType = brandDto.BrandType ?? existingBrand.BrandType;

            if (brandDto.Theme != null)
            {
                existingBrand.Theme.ThemeName = brandDto.Theme.ThemeName ?? existingBrand.Theme.ThemeName;
                existingBrand.Theme.Primary = brandDto.Theme.Primary ?? existingBrand.Theme.Primary;
                existingBrand.Theme.Secondary = brandDto.Theme.Secondary ?? existingBrand.Theme.Secondary;
            }

            if (brandDto.Logo != null)
            {
                existingBrand.Logo.Dark = brandDto.Logo.Dark ?? existingBrand.Logo.Dark;
                existingBrand.Logo.Light = brandDto.Logo.Light ?? existingBrand.Logo.Light;
                existingBrand.Logo.PreferredWidth = brandDto.Logo.PreferredWidth ?? existingBrand.Logo.PreferredWidth;
                existingBrand.Logo.PreferredWidthMobile = brandDto.Logo.PreferredWidthMobile ?? existingBrand.Logo.PreferredWidthMobile;
            }

            existingBrand.Hosts = brandDto.Hosts ?? existingBrand.Hosts;

            if (brandDto.Footer != null)
            {
                existingBrand.Footer.Sections = brandDto.Footer.Sections?
                    .Select(section => new FooterSection
                    {
                        Title = section.Title,
                        Items = section.Items?
                            .Select(item => new FooterItem
                            {
                                Type = item.Type,
                                Text = item.Text,
                                Attributes = item.Attributes
                            }).ToList()
                    }).ToList() ?? existingBrand.Footer.Sections;

                existingBrand.Footer.AppTitle = brandDto.Footer.AppTitle ?? existingBrand.Footer.AppTitle;

                existingBrand.Footer.Apps = brandDto.Footer.Apps?
                    .Select(app => new App
                    {
                        Name = app.Name,
                        Image = app.Image,
                        Url = app.Url
                    }).ToList() ?? existingBrand.Footer.Apps;

                existingBrand.Footer.SocialNetworks = brandDto.Footer.SocialNetworks?
                    .Select(sn => new SocialNetwork
                    {
                        Name = sn.Name,
                        Icon = sn.Icon,
                        Attributes = sn.Attributes
                    }).ToList() ?? existingBrand.Footer.SocialNetworks;

                existingBrand.Footer.Payments = brandDto.Footer.Payments?
                    .Select(payment => new Payment
                    {
                        Title = payment.Title,
                        Flags = payment.Flags?
                            .Select(flag => new PaymentFlag
                            {
                                Name = flag.Name,
                                Icon = flag.Icon,
                                Description = flag.Description
                            }).ToList()
                    }).ToList() ?? existingBrand.Footer.Payments;

                existingBrand.Footer.ImportantReminders = brandDto.Footer.ImportantReminders?
                    .Select(reminder => new ImportantReminder
                    {
                        Label = reminder.Label,
                        Description = reminder.Description
                    }).ToList() ?? existingBrand.Footer.ImportantReminders;
            }

            existingBrand.GtwData = new GtwData
            {
                BranchId = brandDto.GtwData?.BranchId ?? existingBrand.GtwData.BranchId,
                AgentSign = brandDto.GtwData?.AgentSign ?? existingBrand.GtwData.AgentSign
            };

            existingBrand.HomeMessages = brandDto.HomeMessages ?? existingBrand.HomeMessages;
            existingBrand.HotjarIds = new HotjarIds
            {
                MyTravelsFront = brandDto.HotjarIds?.MyTravelsFront ?? existingBrand.HotjarIds.MyTravelsFront,
                CustomerLoyalty = brandDto.HotjarIds?.CustomerLoyalty ?? existingBrand.HotjarIds.CustomerLoyalty
            };

            existingBrand.ProductsRedirect = brandDto.ProductsRedirect?
                .Select(pr => new ProductRedirectInfo
                {
                    Name = pr.Name,
                    Url = pr.Url
                }).ToList() ?? existingBrand.ProductsRedirect;

            existingBrand.RedirectsToLogin = brandDto.RedirectsToLogin ?? existingBrand.RedirectsToLogin;

            // Persistir as alterações
            await _brandRepository.UpdateAsync(id, existingBrand);

            return existingBrand;
        }

    }

}
