# Context Api

Boilerplate para criação de aplicações utilizando .NET 6, estruturado para um ambiente de desenvolvimento moderno e escalável.

---

[![Build Status](#)](#)
[![Quality Gate](#)](#)
[![Coverage](#)](#)
[![Maintainnability](#)](#)
[![Security](#)](#)

---

## 🚀 Como rodar o projeto localmente

### Pré-requisitos

- **.NET SDK** (versão 6.0 ou superior) - [Instale o .NET SDK aqui](https://dotnet.microsoft.com/download/dotnet)
- **Git** (para clonar o repositório).

### Passos para execução

1. Clone o repositório:
   ```bash
   git clone <https://git.cvc.com.br/corp-platform/corp-platform-context>
   cd <corp-platform-context>
   ```

2. Restaure as dependências:
   ```bash
   dotnet restore
   ```

3. Compile o projeto:
   ```bash
   dotnet build
   ```

4. Execute o projeto:
   ```bash
   dotnet run
   ```

5. Acesse a aplicação:
   [http://localhost:5000](http://localhost:5000) (ou [https://localhost:5001](https://localhost:5001) para HTTPS).

---

## 🌐 URLs dos Ambientes

### Desenvolvimento

- **Base URL:** [http://dev.context.com](http://dev.context.com)
- **Documentação:** [http://dev.context.com/swagger](http://dev.context.com/swagger)

### Homologação

- **Base URL:** [http://qa.context.com](http://qa.context.com)
- **Documentação:** [http://qa.context.com/swagger](http://qa.context.com/swagger)

### Produção

- **Base URL:** [http://prod.context.com](http://prod.context.com)
- **Documentação:** [http://prod.context.com/swagger](http://prod.context.com/swagger)

---

## 📂 Arquitetura do Projeto

- **Framework:** .NET 6
- **Estrutura de Diretórios:**
  - `src/` - Contém o código fonte principal dividido em camadas como `Domain`, `Infra` e `Application`.
  - `tests/` - Contém os testes unitários e de integração.

---

## 🔍 Principais Scripts Disponíveis

1. **Restaurar dependências:**
   ```bash
   dotnet restore
   ```

2. **Compilar o projeto:**
   ```bash
   dotnet build
   ```

3. **Executar o projeto:**
   ```bash
   dotnet run
   ```

4. **Rodar testes:**
   ```bash
   dotnet test
   ```

---

## 🛠️ Configurações

### Arquivo `appsettings.json`

Certifique-se de configurar corretamente os parâmetros no arquivo `appsettings.json`. Exemplo:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "************************************************"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "AllowedHosts": "*"
}
```

---

## Jenkins

Acesse o pipeline no Jenkins para integração e deploy contínuo:

[Pipeline Jenkins](#)

---

## SonarQube

Confira a análise de qualidade do código no SonarQube:

[Análise Sonar](#)

---

## Criação de Chamados

- [Time de DevOps](#)
- [Time de Cloud](#)

---

## Arquitetura

Para mais informações sobre a arquitetura, consulte:

- [Documentação da Arquitetura](#)
