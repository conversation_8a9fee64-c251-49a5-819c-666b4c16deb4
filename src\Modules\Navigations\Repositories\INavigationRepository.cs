using System.Collections.Generic;
using System.Threading.Tasks;

namespace PlatformContext.Modules.Navigation
{
    public interface INavigationRepository
    {
        Task<(List<Navigation>, int)> GetAllAsync(int page, int pageSize);
        Task<Navigation> GetByIdAsync(string id);
        Task CreateAsync(Navigation navigation);
        Task UpdateAsync(string id, Navigation updatedNavigation);
        Task DeleteAsync(string id);
    }
}