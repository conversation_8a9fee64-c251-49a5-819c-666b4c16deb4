using Auth.DTOs;
using Auth.Repositories;

namespace Auth.UseCases
{
    public class LoginUseCase
    {
        private readonly IAuthRepository _authRepository;

        public LoginUseCase(IAuthRepository authRepository)
        {
            _authRepository = authRepository;
        }

        public async Task<TokenResponseDto> ExecuteAsync(LoginRequestDto loginRequest)
        {
            try
            {
                // Obter o token SSO
                var ssoTokenResponse = await _authRepository.LoginAsync(loginRequest.Username, loginRequest.Password);

                if (string.IsNullOrEmpty(ssoTokenResponse.AccessToken))
                {
                    throw new Exception("SSO Token is empty or null.");
                }

                // Gerar o token da aplicação
                var appTokenResponse = await _authRepository.GenerateAppTokenAsync(ssoTokenResponse.AccessToken);

                return appTokenResponse;
            }
            catch (UnauthorizedAccessException ex)
            {
                Console.WriteLine($"Erro de autenticação: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erro ao executar o login: {ex.Message}");
                throw;
            }
        }
    }
}
