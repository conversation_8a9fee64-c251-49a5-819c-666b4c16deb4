﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="DotNetEnv" Version="3.1.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="6.0.20" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.0" />
    <PackageReference Include="MongoDB.Driver" Version="3.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="7.1.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="6.5.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.3.0" />
  </ItemGroup>

  <ItemGroup>
    <None Include="src\Properties\launchSettings.json" />
    <None Remove="src\Domain\**" />
    <None Remove="src\Infra\**" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="src\Domain\**" />
    <Compile Remove="src\Infra\**" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Remove="src\Domain\**" />
    <EmbeddedResource Remove="src\Infra\**" />
  </ItemGroup>

  <ItemGroup>
    <Content Remove="src\Domain\**" />
    <Content Remove="src\Infra\**" />
  </ItemGroup>
</Project>
