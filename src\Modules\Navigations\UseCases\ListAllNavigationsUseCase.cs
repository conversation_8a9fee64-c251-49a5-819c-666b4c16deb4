using System.Threading.Tasks;
using System.Collections.Generic;

namespace PlatformContext.Modules.Navigation
{
    public class ListAllNavigationsUseCase
    {
        private readonly INavigationRepository _navigationRepository;

        public ListAllNavigationsUseCase(INavigationRepository navigationRepository)
        {
            _navigationRepository = navigationRepository;
        }

        public async Task<(List<Navigation>, int)> ExecuteAsync(int page, int pageSize)
        {
            return await _navigationRepository.GetAllAsync(page, pageSize);
        }
    }
}