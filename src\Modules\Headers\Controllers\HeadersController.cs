using Microsoft.AspNetCore.Mvc;
using PlatformContext.Modules.Headers.Dtos;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PlatformContext.Modules.Headers
{
    [ApiController]
    [Route("api/headers")]
    public class HeadersController : ControllerBase
    {
        private readonly CreateHeaderUseCase _createHeaderUseCase;
        private readonly GetAllHeadersUseCase _getAllHeadersUseCase;
        private readonly GetHeaderByIdUseCase _getHeaderByIdUseCase;
        private readonly UpdateHeaderUseCase _updateHeaderUseCase;
        private readonly DeleteHeaderUseCase _deleteHeaderUseCase;

        public HeadersController(
            CreateHeaderUseCase createHeaderUseCase,
            GetAllHeadersUseCase getAllHeadersUseCase,
            GetHeaderByIdUseCase getHeaderByIdUseCase,
            UpdateHeaderUseCase updateHeaderUseCase,
            DeleteHeaderUseCase deleteHeaderUseCase)
        {
            _createHeaderUseCase = createHeaderUseCase;
            _getAllHeadersUseCase = getAllHeadersUseCase;
            _getHeaderByIdUseCase = getHeaderByIdUseCase;
            _updateHeaderUseCase = updateHeaderUseCase;
            _deleteHeaderUseCase = deleteHeaderUseCase;
        }

        [HttpPost]
        public async Task<IActionResult> CreateHeader([FromBody] HeadersDto createHeaderDto)
        {
            if (createHeaderDto == null || createHeaderDto.Items == null || createHeaderDto.Items.Count == 0)
            {
                return BadRequest("Invalid request body.");
            }

            var headerId = await _createHeaderUseCase.ExecuteAsync(createHeaderDto);
            return CreatedAtAction(nameof(CreateHeader), new { id = headerId }, new { id = headerId });
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetHeaderById(string id)
        {
            if (string.IsNullOrWhiteSpace(id))
            {
                return BadRequest("Id cannot be null or empty.");
            }

            var header = await _getHeaderByIdUseCase.ExecuteAsync(id);
            if (header == null)
            {
                return NotFound();
            }

            return Ok(header);
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateHeader(string id, [FromBody] HeadersDto updateHeaderDto)
        {
            if (string.IsNullOrWhiteSpace(id) || updateHeaderDto == null)
            {
                return BadRequest("Invalid request body or id.");
            }

            await _updateHeaderUseCase.ExecuteAsync(id, updateHeaderDto);
            return NoContent();
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteHeader(string id)
        {
            if (string.IsNullOrWhiteSpace(id))
            {
                return BadRequest("Id cannot be null or empty.");
            }

            await _deleteHeaderUseCase.ExecuteAsync(id);
            return NoContent();
        }

        [HttpGet("all")]
        public async Task<IActionResult> GetAll([FromQuery] string? nameHeader, [FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10)
        {
            var (headers, totalRecords ) = await _getAllHeadersUseCase.ExecuteAsync(nameHeader, pageNumber, pageSize);
            var response = new
            {
                data = headers,
                totalRecords,
                pageNumber,
                pageSize
            };

            return Ok(response);
        }

    }
}