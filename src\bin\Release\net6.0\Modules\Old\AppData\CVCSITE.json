﻿{
   "id": "CVC",
   "name": "CVC",
   "brand": "CVC",
   "hosts": [
      "www.cvc.com.br",
      "mobile.cvc.com.br",
      "cvc",
      "local.cvc",
      "ti.cvc.com.br",
      "qa.cvc.com.br",
      "cvc.com.br",
      "ti-mobile.cvc.com.br",
      "qa-mobile.cvc.com.br",
      "ti.atlas.cvc.com.br",
      "atlas-qa.cvc.com.br",
      "qa.atlas.cvc.com.br",
      "atlas.cvc.com.br",
      "atlaspiloto.cvc.com.br",
      "atlaspreprod.cvc.com.br",
      "ti.agentesb2b.cvccorp.com.br",
      "qa.agentesb2b.cvccorp.com.br",
      "agentesb2b.cvccorp.com.br",
      "cvcagentes.cvc.com.br",
      "ti.cvcagentes.cvc.com.br",
      "qa.cvcagentes.cvc.com.br",
      "piloto.cvc.com.br"
   ],
   "theme": {
      "themeName": "cvc",
      "air": "#038EAD",
      "alert": "#FF9D00",
      "approved": "#11AF71",
      "backdrop": "rgba(0,0,0, .3)",
      "backdrop70": "rgba(0,0,0, .7)",
      "backdrop100": "#000",
      "border": "#DDD",
      "canceled": "#D73A21",
      "car": "#0074C5",
      "contrastSecondaryText": "#333",
      "disabled": "#EFEFEF",
      "error": "#D73A21",
      "hotel": "#BF0268",
      "offwhite": "#F2F5F8",
      "packageColor": "#E55402",
      "primary": "#263c70",
      "primaryAlpha": "rgba(75, 123, 227, .15)",
      "primaryAlphaDark": "rgba(255, 255, 255, 0.15)",
      "primaryDark": "#192748",
      "primaryDarkHover": "#335094",
      "primaryLight": "#263C70",
      "primaryLighter": "rgba(72, 123, 227, 0.05)",
      "primaryText": "#333",
      "processed": "#FF5D00",
      "secondary": "#FFDC2C",
      "secondaryDark": "#C7A600",
      "secondaryHover": "#FFE561",
      "secondaryLighter": "rgba(255, 220, 44, 0.1)",
      "secondaryText": "#999",
      "spot": "#CC0E9E",
      "success": "#11af71",
      "successHover": "#16DF90",
      "tabs": "#2374BB",
      "tabsHover": "#237FD0",
      "white": "#FFF",
      "white30": "rgba(255,255,255, .3)",
      "regular": "HindMRegular",
      "bold": "HindMBold"
   },
   "menu": {
      "hiddenPrincipals": [],
      "principals": [
         {
            "product": "air",
            "label": "Passagens",
            "link": "/passagens-aereas",
            "target": null,
            "icon": "Air",
            "id": "menu-passagens"
         },
         {
            "product": "hotel",
            "label": "Hotéis",
            "link": "/hotel",
            "target": null,
            "icon": "Hotel",
            "id": "menu-hoteis"
         },
         {
            "product": "pacotes",
            "label": "Pacotes",
            "link": "/pacotes-turisticos",
            "target": null,
            "icon": "PackageIcon",
            "id": "menu-pacotes"
         },
         {
            "product": "cruzeiros",
            "label": "Cruzeiros",
            "link": "https://cruzeirosonline.cvc.com.br",
            "target": "_blank",
            "icon": "Cruise",
            "id": null
         },
         {
            "product": "carros",
            "label": "Carros ",
            "link": "/aluguel-de-carros",
            "target": null,
            "icon": "Car",
            "id": "menu-cars"
         },
         {
            "product": "promocoes",
            "label": "Promoções",
            "link": "https://www.cvc.com.br/lp/promocoes/saldao-de-pacotes",
            "target": "_blank",
            "icon": "https://almundo-com-res.cloudinary.com/raw/upload/v1730147179/chui-icons/filled/service/FilledDiscountBubble.svg",
            "id": null
         },
         {
            "product": "cvcmeleva",
            "label": "CVC me leva",
            "link": "https://www.cvc.com.br/lp/promocoes/cvcmeleva-site",
            "target": "_blank",
            "icon": "https://almundo-com-res.cloudinary.com/image/upload/v1706712140/CVC/platform/icons/World.svg",
            "id": null
         }
      ],
      "linkTravelSuggestions": [
         {
            "title": "Destinos",
            "description": "Disney World",
            "href": "https://www.cvcmais.com.br/walt-disney-world-resort",
            "src": "https://www.cvc.com.br/imageproc/image/upload/f_auto/v1/cvcmais/home/<USER>"
         },
         {
            "title": "Destinos",
            "description": "Rio de Janeiro",
            "href": "https://www.cvcmais.com.br/rio-de-janeiro",
            "src": "https://www.cvc.com.br/imageproc/image/upload/f_auto/v1/cvcmais/home/<USER>"
         },
         {
            "title": "Destinos",
            "description": "Porto Seguro",
            "href": "https://www.cvcmais.com.br/porto-seguro",
            "src": "https://www.cvc.com.br/imageproc/image/upload/f_auto/v1/cvcmais/home/<USER>"
         },
         {
            "title": "Destinos",
            "description": "Natal",
            "href": "https://www.cvcmais.com.br/natal",
            "src": "https://www.cvc.com.br/imageproc/image/upload/f_auto/v1/cvcmais/home/<USER>"
         },
         {
            "title": "Destinos",
            "description": "Serra Gaúcha",
            "href": "https://www.cvcmais.com.br/serra-gaucha",
            "src": "https://www.cvc.com.br/imageproc/image/upload/f_auto/v1/cvcmais/home/<USER>"
         },
         {
            "title": "Destinos",
            "description": "Maceió",
            "href": "https://www.cvcmais.com.br/maceio",
            "src": "https://www.cvc.com.br/imageproc/image/upload/f_auto/v1/cvcmais/home/<USER>"
         },
         {
            "title": "Destinos",
            "description": "Ceará",
            "href": "https://www.cvcmais.com.br/ceara",
            "src": "https://www.cvc.com.br/imageproc/image/upload/f_auto/v1/cvcmais/home/<USER>"
         },
         {
            "title": "Destinos",
            "description": "Porto de Galinhas",
            "href": "https://www.cvcmais.com.br/porto-de-galinhas",
            "src": "https://www.cvc.com.br/imageproc/image/upload/f_auto/v1/cvcmais/home/<USER>"
         },
         {
            "title": "Destinos",
            "description": "Balneário Camboriú",
            "href": "https://www.cvcmais.com.br/balneario-camboriu",
            "src": "https://www.cvc.com.br/imageproc/image/upload/f_auto/v1/cvcmais/home/<USER>"
         },
         {
            "title": "Destinos",
            "description": "Buenos Aires",
            "href": "https://www.cvcmais.com.br/buenos-aires",
            "src": "https://www.cvc.com.br/imageproc/image/upload/f_auto/v1/cvcmais/home/<USER>"
         },
         {
            "title": "Destinos",
            "description": "Santa Catarina",
            "href": "https://www.cvcmais.com.br/santa-catarina",
            "src": "https://www.cvc.com.br/imageproc/image/upload/f_auto/v1/cvcmais/home/<USER>"
         }
      ],
      "hiddenOthers": null,
      "others": [
         {
            "product": "club",
            "label": "Clube CVC",
            "link": "/clube-cvc",
            "target": "_blank",
            "icon": "Club",
            "id": "menu-club"
         },
         {
            "product": "disney",
            "label": "Disney",
            "link": "https://www.cvc.com.br/lp/pacotes-de-viagem/internacional/disney",
            "target": null,
            "icon": "Disney",
            "id": "menu-disney"
         },
         {
            "product": null,
            "label": "App CVC",
            "link": "https://www.cvc.com.br/lp/app-cvc-viagens",
            "target": "_blank",
            "icon": "Roof",
            "id": null
         },
         {
            "product": null,
            "label": "CVC+",
            "link": "http://www.cvcmais.com.br/",
            "target": "_blank",
            "icon": "https://www.cvc.com.br/imageproc/image/upload/v1653509381/cvcmais/play.png",
            "id": null
         },
         {
            "product": "ingresso",
            "label": "Ingressos",
            "link": "/ingresso",
            "target": "_blank",
            "icon": "Ingresso",
            "id": null
         },
         {
            "product": "rodoviario",
            "label": "Ônibus",
            "link": "/rodoviario/rodofacil/search",
            "target": "_blank",
            "icon": "https://almundo-com-res.cloudinary.com/image/upload/v1706712140/CVC/platform/icons/bus.svg",
            "id": null
         },
         {
            "product": "seguro",
            "label": "Seguro Viagem",
            "link": "/seguro-viagem",
            "target": null,
            "icon": "Insurance",
            "id": null
         },
         {
            "product": "tours",
            "label": "Circuitos ",
            "link": "/tours",
            "target": null,
            "icon": "MultipleDestinations",
            "id": "menu-tours"
         },
         {
            "product": null,
            "label": "Vale-Viagem",
            "link": "/vale-viagem.aspx",
            "target": "_blank",
            "icon": "https://www.submarinoviagens.com.br/imageproc/w_24,h_24/v1/CVC/platform/icons/gift.png",
            "id": null
         }
      ],
      "phone": " (11) 3003-9282",
      "phoneSales": "(11) 3003-9282",
      "help": "/atendimento/faq",
      "register": "/minhasviagens/cadastro",
      "login": "/minhasviagens/"
   },
   "footer": {
      "sections": [
         {
            "title": "CVC Brasil",
            "items": [
               {
                  "type": "GTMLINK",
                  "text": "Sobre nós",
                  "attributes": {
                     "data-gtm-event-category": "cvc:geral",
                     "data-gtm-event-action": "footer:clique",
                     "data-gtm-event-label": "Sobre a CVC",
                     "target": "_blank",
                     "href": "/institucional/sobre-a-cvc-nossa-historia",
                     "title": "Sobre a CVC",
                     "rel": "noopener noreferrer"
                  }
               },
               {
                  "type": "TEXTLINK",
                  "text": "Sala de Imprensa",
                  "attributes": {
                     "href": "mailto:<EMAIL>"
                  }
               },
               {
                  "type": "GTMLINK",
                  "text": "Trabalhe Conosco - Matriz CVC",
                  "attributes": {
                     "data-gtm-event-category": "cvc:geral",
                     "data-gtm-event-action": "footer:clique",
                     "data-gtm-event-label": "Trabalhe Conosco - Matriz CVC",
                     "target": "_blank",
                     "href": "https://cvccorp.gupy.io/",
                     "title": "Trabalhe Conosco - Matriz CVC",
                     "rel": "noopener noreferrer"
                  }
               },
               {
                  "type": "GTMLINK",
                  "text": "Trabalhe Conosco - Lojas CVC",
                  "attributes": {
                     "data-gtm-event-category": "cvc:geral",
                     "data-gtm-event-action": "footer:clique",
                     "data-gtm-event-label": "Trabalhe Conosco - Lojas CVC",
                     "target": "_blank",
                     "href": "https://passaportefranquia.cvc.com.br/",
                     "title": "Trabalhe Conosco - Lojas CVC",
                     "rel": "noopener noreferrer"
                  }
               },
               {
                  "type": "GTMLINK",
                  "text": "Seja um Franqueado",
                  "attributes": {
                     "data-gtm-event-category": "cvc:geral",
                     "data-gtm-event-action": "footer:clique",
                     "data-gtm-event-label": "Seja um Franqueado",
                     "target": "_blank",
                     "href": "https://franquias.cvc.com.br/",
                     "title": "Seja um Franqueado",
                     "rel": "noopener noreferrer"
                  }
               }
            ]
         },
         {
            "title": "Central de atendimento",
            "items": [
               {
                  "type": "TEXT",
                  "text": "Suporte Pós Venda (Compras no site)",
                  "attributes": {
                     "weight": "bold"
                  }
               },
               {
                  "type": "TEXT",
                  "text": "11 3003 9282 • Opção 2 | 1 URA"
               },
               {
                  "type": "TEXT",
                  "text": "Segunda à Sábado: 09h às 21h"
               },
               {
                  "type": "TEXT",
                  "text": "Central de relacionamento ao cliente (CRC)",
                  "attributes": {
                     "weight": "bold"
                  }
               },
               {
                  "type": "TEXT",
                  "text": "11 3003-9282 • Opção 2 | 2 URA",
                  "attributes": null
               },
               {
                  "type": "TEXT",
                  "text": "Segunda à Sexta: 09h às 20h",
                  "attributes": null
               },
               {
                  "type": "TEXT",
                  "text": "Sábado e Feriados Nacionais: 09h às 16h"
               },
               {
                  "type": "GTMLINK",
                  "text": "Central de Ajuda",
                  "attributes": {
                     "data-gtm-event-category": "cvc:geral",
                     "data-gtm-event-action": "footer:clique",
                     "data-gtm-event-label": "Atendimento ao cliente",
                     "target": "_self",
                     "href": "/atendimento/faq",
                     "title": "Atendimento ao cliente",
                     "rel": "noopener noreferrer"
                  }
               }
            ]
         },
         {
            "title": "Termos",
            "items": [
               {
                  "type": "GTMLINK",
                  "text": "Condições de uso do site",
                  "attributes": {
                     "data-gtm-event-category": "cvc:geral",
                     "data-gtm-event-action": "footer:clique",
                     "data-gtm-event-label": "Condições de uso do site",
                     "target": "_blank",
                     "href": "/termos-e-condicoes-de-uso",
                     "title": "Condições de uso do site",
                     "rel": "noopener noreferrer"
                  }
               },
               {
                  "type": "GTMLINK",
                  "text": "Condições Gerais",
                  "attributes": {
                     "data-gtm-event-category": "cvc:geral",
                     "data-gtm-event-action": "footer:clique",
                     "data-gtm-event-label": "Condições Gerais",
                     "target": "_blank",
                     "href": "/condicoes-gerais-para-aquisicao-de-servicos-de-turismo",
                     "title": "Condições Gerais",
                     "rel": "noopener noreferrer"
                  }
               },
               {
                  "type": "GTMLINK",
                  "text": "Politica de privacidade",
                  "attributes": {
                     "data-gtm-event-category": "cvc:geral",
                     "data-gtm-event-action": "footer:clique",
                     "data-gtm-event-label": "Política de Privacidade",
                     "target": "_blank",
                     "href": "/politica-de-privacidade",
                     "title": "Política de Privacidade",
                     "rel": "noopener noreferrer"
                  }
               },
               {
                  "type": "GTMLINK",
                  "text": "Código de Conduta Ética",
                  "attributes": {
                     "data-gtm-event-category": "cvc:geral",
                     "data-gtm-event-action": "footer:clique",
                     "data-gtm-event-label": "Código de Conduta Ética",
                     "target": "_blank",
                     "href": "https://www.cvccorp.com.br/contato-e-imprensa/contato/canal-de-etica-e-codigo-de-conduta-etica/",
                     "title": "Código de Conduta Ética",
                     "rel": "noopener noreferrer"
                  }
               },
               {
                  "type": "GTMLINK",
                  "text": "Termos de Uso Clube CVC",
                  "attributes": {
                     "data-gtm-event-category": "cvc:geral",
                     "data-gtm-event-action": "footer:clique",
                     "data-gtm-event-label": "Termos de Uso Clube CVC",
                     "target": "_blank",
                     "href": "https://clube.cvc.com.br/termo/regulamento_clubecvc.pdf",
                     "title": "Termos de Uso Clube CVC",
                     "rel": "noopener noreferrer"
                  }
               },
               {
                  "type": "GTMLINK",
                  "text": "Termos de Uso Clube CVC Super",
                  "attributes": {
                     "data-gtm-event-category": "cvc:geral",
                     "data-gtm-event-action": "footer:clique",
                     "data-gtm-event-label": "Termos de Uso Clube CVC Super",
                     "target": "_blank",
                     "href": "https://clube.cvc.com.br/termo/regulamento_clubesuper.pdf",
                     "title": "Termos de Uso Clube CVC Super",
                     "rel": "noopener noreferrer"
                  }
               }
            ]
         }
      ],
      "appTitle": "Baixe nosso aplicativo",
      "apps": [
         {
            "name": "App Store",
            "image": "https://www.cvc.com.br/imageproc/image/upload/v1/global/icons/social/socialAppstore.svg",
            "url": "https://apps.apple.com/br/app/minha-cvc/id1437655417"
         },
         {
            "name": "Google Play",
            "image": "https://www.cvc.com.br/imageproc/image/upload/v1/global/icons/social/socialGoogleplay.svg",
            "url": "https://play.google.com/store/apps/details?id=com.cvc.minhacvc"
         }
      ],
      "socialNetworksTitle": "Siga ofertas nas redes",
      "socialNetworks": [
         {
            "name": "Facebook",
            "icon": "https://www.cvc.com.br/imageproc/image/upload/v1/global/icons/social/socialFacebook.svg",
            "attributes": {
               "data-gtm-event-category": "cvc:geral",
               "data-gtm-event-action": "footer:clique",
               "data-gtm-event-label": "Facebook",
               "target": "_blank",
               "href": "https://www.facebook.com/cvcviagens"
            }
         },
         {
            "name": "Instagram",
            "icon": "https://www.cvc.com.br/imageproc/image/upload/v1/global/icons/social/socialInstagram.svg",
            "attributes": {
               "data-gtm-event-category": "cvc:geral",
               "data-gtm-event-action": "footer:clique",
               "data-gtm-event-label": "Instagram",
               "target": "_blank",
               "href": "https://www.instagram.com/cvcviagens"
            }
         },
         {
            "name": "YouTube",
            "icon": "https://www.cvc.com.br/imageproc/image/upload/v1/global/icons/social/socialYoutube.svg",
            "attributes": {
               "data-gtm-event-category": "cvc:geral",
               "data-gtm-event-action": "footer:clique",
               "data-gtm-event-label": "Youtube",
               "target": "_blank",
               "href": "https://www.youtube.com/cvc"
            }
         },
         {
            "name": "Pinterest",
            "icon": "https://www.cvc.com.br/imageproc/image/upload/v1/global/icons/social/socialPinterest.svg",
            "attributes": {
               "data-gtm-event-category": "cvc:geral",
               "data-gtm-event-action": "footer:clique",
               "data-gtm-event-label": "Pinterest",
               "target": "_blank",
               "href": "https://br.pinterest.com/cvcviagens"
            }
         }
      ],
      "payments": [
         {
            "title": "Formas de pagamento",
            "flags": [
               {
                  "name": "Visa",
                  "icon": "https://www.cvc.com.br/imageproc/image/upload/v1/global/logos/cards/visa.svg"
               },
               {
                  "name": "Mastercard",
                  "icon": "https://www.cvc.com.br/imageproc/image/upload/v1/global/logos/payments/mastercard.svg"
               },
               {
                  "name": "Elo",
                  "icon": "https://www.cvc.com.br/imageproc/image/upload/v1/global/logos/cards/elo.svg"
               },
               {
                  "name": "AmericanExpress",
                  "icon": "https://www.cvc.com.br/imageproc/image/upload/v1/global/logos/cards/american-express.svg"
               },
               {
                  "name": "Diners",
                  "icon": "https://www.cvc.com.br/imageproc/image/upload/v1/global/logos/cards/diners.svg"
               },
               {
                  "name": "Hipercard",
                  "icon": "https://www.cvc.com.br/imageproc/image/upload/v1/global/logos/cards/hipercard.svg"
               }
            ]
         }
      ],
      "paymentsV2": [
         {
            "title": "Formas de pagamentos",
            "options": [
               {
                  "title": "Cartão de crédito",
                  "description": "Até 2 cartões da mesma titularidade",
                  "flags": [
                     {
                        "name": "Visa",
                        "icon": "https://almundo-com-res.cloudinary.com/raw/upload/v1730741007/chui-icons/flags/creditCard/ghost/GhostVisa.svg"
                     },
                     {
                        "name": "Mastercard",
                        "icon": "https://almundo-com-res.cloudinary.com/raw/upload/v1730741017/chui-icons/flags/creditCard/ghost/GhostMastercard.svg"
                     },
                     {
                        "name": "Elo",
                        "icon": "https://almundo-com-res.cloudinary.com/raw/upload/v1730741016/chui-icons/flags/creditCard/ghost/GhostElo.svg"
                     },
                     {
                        "name": "AmericanExpress",
                        "icon": "https://almundo-com-res.cloudinary.com/raw/upload/v1730741006/chui-icons/flags/creditCard/ghost/GhostAmex.svg"
                     },
                     {
                        "name": "Diners",
                        "icon": "https://almundo-com-res.cloudinary.com/raw/upload/v1730741006/chui-icons/flags/creditCard/ghost/GhostDiners.svg"
                     },
                     {
                        "name": "Hipercard",
                        "icon": "https://almundo-com-res.cloudinary.com/raw/upload/v1730741017/chui-icons/flags/creditCard/ghost/GhostHipercard.svg"
                     }
                  ]
               },
               {
                  "title": "Outras formas",
                  "flags": [
                     {
                        "name": "Pix",
                        "icon": "https://www.cvc.com.br/imageproc/image/upload/v1/global/logos/cards/pix.svg"
                     },
                     {
                        "name": "Nubank",
                        "icon": "https://www.cvc.com.br/imageproc/image/upload/v1/global/logos/cards/nuPay.svg",
                        "description": "até 24x para clientes Nubank"
                     }
                  ]
               },
               {
                  "title": "Formas exclusivas nas lojas CVC",
                  "flags": [
                     {
                        "name": "FGTS"
                     },
                     {
                        "name": "+2 cartões de crédito"
                     },
                     {
                        "name": "Boleto parcelado"
                     }
                  ]
               }
            ]
         }
      ],
      "importantReminder": [
         {
            "label": "Importante: ",
            "description": "*As novas formas de pagamento; 2 cartões e PIX são válidos para todos os produtos exceto Aéreo e Cruzeiros."
         }
      ],
      "texts": [
         {
            "text": "CVC Brasil Operadora e Agência de Viagens S.A - CNPJ:",
            "textOnBreakMobile": "10.760.260/0001-19"
         },
         {
            "text": "Rua da Catequese, 227, 11 andar, sala 111 | ",
            "textOnBreakMobile": "Bairro Jardim, Santo André - SP"
         },
         {
            "text": "CEP: 09090-401",
            "textOnBreakMobile": ""
         }
      ]
   },
   "logo": {
      "dark": "https://www.cvc.com.br/imageproc/image/upload/f_auto,q_auto:eco,w_53/v1625058605/global/logos/brand/CVC_Azul_Verde.svg",
      "light": "https://www.cvc.com.br/imageproc/image/upload/f_auto,q_auto:eco,w_53/v1625058605/global/logos/brand/CVC_Amarelo_Verde.svg",
      "preferredWidth": "53px",
      "preferredWidthMobile": "43px"
   },
   "gtwData": {
      "branchId": "1000",
      "agentSign": "WEB"
   },
   "gtm": "GTM-K9MKT2Q",
   "omnilogicScript": "cvc2",
   "imageProcessorPath": "https://www.cvc.com.br/imageproc/",
   "headerImageBg": {
      "desktop": "https://almundo-com-res.cloudinary.com/image/upload/v1606240709/CVC/home/<USER>",
      "mobile": "https://almundo-com-res.cloudinary.com/image/upload/v1606240709/CVC/home/<USER>"
   },
   "customImageBgProduct": {
      "productNames": {
         "disney": {
            "desktop": "https://ti.submarinoviagens.com.br/platform/assets/tickets-disney/_next/static/images/img_home_banner-78469bd7833ebf77c208e1b56cb0bb23.png",
            "mobile": "https://ti.submarinoviagens.com.br/platform/assets/tickets-disney/_next/static/images/bg_disney_mobile-2e70afb7d44e43acdc47508ae0bba57c.png"
         }
      }
   },
   "homeMotorImageBg": null,
   "favicon": {
      "small": "https://almundo-com-res.cloudinary.com/image/upload/v1697557619/global/logos/favicon/Favicon_CVC_16x16_BlackFriday.svg",
      "big": "https://almundo-com-res.cloudinary.com/image/upload/v1625080334/global/logos/favicon/Favicon_CVC_32x32.png"
   },
   "compl": null,
   "homeMessages": {
      "index": "Economize * muito com as melhores ofertas de viagens *",
      "pacotes-turisticos": "* Preços incríveis nos * pacotes de viagens CVC",
      "pacotes": "* Preços incríveis nos * pacotes de viagens CVC",
      "hotel": "* Hotéis, pousadas e resorts com  * preços imperdíveis",
      "passagens-aereas": "Passagens aéreas baratas * para os melhores destinos *",
      "air": "Passagens aéreas baratas * para os melhores destinos *",
      "carros": "* Alugue o carro ideal com * preços imperdíveis",
      "ingresso": "* Diversão garantida com ingressos pelo * melhor preço",
      "disney": "Conheça a * Disney * de Orlando com a CVC",
      "seguro": "* O *seguro ideal* para sua viagem ser mais tranquila"
   },
   "hotjarIds": {
      "myTravelsFront": "1840425",
      "customerLoyalty": "1840425"
   },
   "termConditionLink": "/termos-e-condicoes-de-uso",
   "privacityLink": "/politica-de-privacidade",
   "featureToggles": [
      "NEW_LOGIN",
      "PROFILE"
   ],
   "pendingPaymentContact": "(11) 3003-9282",
   "productsRedirect": [
      {
         "name": "cruzeiros",
         "url": "https://cruzeirosonline.cvc.com.br/"
      }
   ],
   "redirectsToLogin": [
      "mytravels/",
      "mypoints/"
   ]
}
