using System.Collections.Generic;
using System.Threading.Tasks;
using PlatformContext.Modules.Headers.Dtos;

namespace PlatformContext.Modules.Headers
{
    public class GetAllHeadersUseCase
    {
        private readonly IHeadersRepository _headersRepository;

        public GetAllHeadersUseCase(IHeadersRepository headersRepository)
        {
            _headersRepository = headersRepository;
        }

        public async Task<(List<HeadersDto> Headers, long TotalCount)> ExecuteAsync(string? nameHeader, int pageNumber = 1, int pageSize = 10)
        {
            var (headerList, totalCount) = await _headersRepository.GetAllAsync(nameHeader, pageNumber, pageSize);

            var headersDto = headerList.ConvertAll(header => new HeadersDto
            {
                Id = header.Id,
                Name = header.Name,
                Items = header.Headers.Items.ConvertAll(item => new ItemDto
                {
                    Id = item.Id,
                    HeaderTitleMessages = item.HeaderTitleMessages
                })
            });

            return (headersDto, totalCount);
        }

    }
}
