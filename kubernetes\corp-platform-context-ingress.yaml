apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: corp-platform-context-ingress
  namespace: corp-platform
  annotations:
    kubernetes.io/ingress.class: "nginx-private"
    nginx.org/ssl-backends: "corp-platform-context-service"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
    nginx.ingress.kubernetes.io/enable-cors: "true"
spec:
  rules:
    - host: corp-platform-context.__SERVICE_ENV__-cvc.com.br
      http:
        paths:
          - backend:
              serviceName: corp-platform-context-service
              servicePort: 80
