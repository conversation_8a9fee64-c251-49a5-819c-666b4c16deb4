apiVersion: v1
kind: ConfigMap
metadata:
  name: corp-platform-context
  namespace: corp-platform
data:
  ADDITIONAL_OPTS: " "
  PREFIX: corp-platform-context
  VAULT_HOST: vault.prod.cvc.intra
  VAULT_SCHEME: http
  CONSUL_HOST: consul.prod.cvc.intra
  CONSUL_PORT: "8500"
  ASPNETCORE_ENVIRONMENT: prod
  PROJECT_NAME: sub-backend-flights
  MongoDbSettings__ConnectionString: "vault:secret/corp-platform-context#MONGO_HOST"
  MongoDbSettings__DatabaseName: "vault:secret/corp-platform-context#MONGO_NAME"