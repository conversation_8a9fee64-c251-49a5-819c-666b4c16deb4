namespace PlatformContext.Modules.Headers.Dtos
{
    public class HeadersDto
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public List<ItemDto> Items { get; set; } = new List<ItemDto>();
    }

    public class ItemDto
    {
        public string Id { get; set; } = string.Empty;
        public List<string> HeaderTitleMessages { get; set; } = new List<string>();
    }
}