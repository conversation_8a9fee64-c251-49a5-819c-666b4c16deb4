using System.Threading.Tasks;

namespace PlatformContext.Modules.Site
{
    public class DeleteSiteUseCase
    {
        private readonly ISiteRepository _siteRepository;

        public DeleteSiteUseCase(ISiteRepository siteRepository)
        {
            _siteRepository = siteRepository;
        }

        public async Task<bool> ExecuteAsync(string id)
        {
            var site = await _siteRepository.GetByIdAsync(id);
            if (site == null)
            {
                return false; // Retorna false se o site não for encontrado
            }

            await _siteRepository.DeleteAsync(id);
            return true; // Retorna true se a exclusão for bem-sucedida
        }
    }
}