namespace PlatformContext.Modules.Brand
{
    public class GetBrandByIdUseCase
    {
        private readonly IBrandRepository _brandRepository;

        public GetBrandByIdUseCase(IBrandRepository brandRepository)
        {
            _brandRepository = brandRepository;
        }

        public async Task<Brand> ExecuteAsync(string id)
        {
            return await _brandRepository.GetByIdAsync(id);
        }
    }
}