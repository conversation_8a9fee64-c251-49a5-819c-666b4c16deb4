using System.Linq;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using PlatformContext.Services;
using PlatformContext.Services.Model;

namespace PlatformContext.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class EngineController : ControllerBase
    {
        private readonly ILogger<EngineController> _logger;

        public EngineController(ILogger<EngineController> logger)
        {
            _logger = logger;
        }

        [HttpGet]
        public ActionResult<EngineResponse> Get([FromQuery] string brand)
        {
            if (string.IsNullOrEmpty(brand))
            {
                return BadRequest("O parâmetro 'brand' é obrigatório.");
            }

            var brandContext = Data.Brands.FirstOrDefault(b => b.Id.Equals(brand, System.StringComparison.OrdinalIgnoreCase));

            if (brandContext == null)
            {
                return NotFound($"Brand '{brand}' não encontrada.");
            }

            Data.LoadNavigationData(brand);

            var items = Data.Navigation?.Items?.Select(item => new NavigationItem
            {
                Id = item.Id,
                Label = item.Label,
                Href = item.Href,
                Icon = item.Icon,
            }).ToList() ?? new List<NavigationItem>();

            var engineResponse = new EngineResponse
            {
                Navigation = new Navigation { Items = items }
            };

            return Ok(engineResponse);
        }
    }
}
